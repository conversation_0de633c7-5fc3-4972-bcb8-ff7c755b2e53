@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', sans-serif;
  }
  
  body {
    @apply bg-zillow-gray text-zillow-dark-gray;
  }
}

@layer components {
  .btn-primary {
    @apply bg-zillow-blue text-white px-4 py-2 rounded-lg font-medium hover:bg-zillow-blue-dark transition-colors duration-200;
  }
  
  .btn-secondary {
    @apply bg-white text-zillow-blue border-2 border-zillow-blue px-4 py-2 rounded-lg font-medium hover:bg-zillow-blue-light transition-colors duration-200;
  }
  
  .btn-success {
    @apply bg-success-green text-white px-4 py-2 rounded-lg font-medium hover:bg-green-600 transition-colors duration-200;
  }
  
  .card {
    @apply bg-white rounded-xl p-6 shadow-zillow;
  }
  
  .card-hover {
    @apply card hover:shadow-zillow-hover transition-shadow duration-200;
  }
  
  .input-field {
    @apply w-full px-4 py-2 border-2 border-zillow-border rounded-lg focus:border-zillow-blue focus:outline-none transition-colors duration-200;
  }
  
  .select-field {
    @apply w-full px-4 py-2 border-2 border-zillow-border rounded-lg focus:border-zillow-blue focus:outline-none transition-colors duration-200 bg-white;
  }
  
  .stat-card {
    @apply card hover:transform hover:-translate-y-1 transition-all duration-200;
  }
  
  .property-card {
    @apply card-hover cursor-pointer border border-zillow-border hover:border-zillow-blue;
  }
  
  .navbar-item {
    @apply flex items-center px-6 py-3 text-white hover:bg-white/10 transition-colors duration-200 border-l-4 border-transparent hover:border-white;
  }
  
  .navbar-item.active {
    @apply bg-white/10 border-white;
  }
  
  .loading-spinner {
    @apply animate-spin rounded-full h-8 w-8 border-b-2 border-zillow-blue;
  }
  
  .notification {
    @apply fixed top-5 right-5 px-6 py-4 rounded-lg shadow-lg z-50 font-medium max-w-sm;
  }
  
  .notification-success {
    @apply bg-success-green text-white;
  }
  
  .notification-error {
    @apply bg-error-red text-white;
  }
  
  .notification-info {
    @apply bg-zillow-blue text-white;
  }
}

@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}

/* Mapbox GL overrides */
.mapboxgl-popup-content {
  @apply rounded-lg shadow-lg;
}

.mapboxgl-popup-close-button {
  @apply text-zillow-blue hover:text-zillow-blue-dark;
}

/* Enhanced map marker styles */
.custom-marker {
  cursor: pointer;
  transition: all 0.2s ease;
}

.custom-marker:hover {
  transform: scale(1.05);
  z-index: 1000;
}

.mapboxgl-popup {
  max-width: 350px !important;
}

.mapboxgl-popup-content {
  padding: 0 !important;
  border-radius: 12px !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
}

/* Map controls styling */
.mapboxgl-ctrl-group {
  @apply shadow-lg rounded-lg;
}

.mapboxgl-ctrl-group button {
  @apply hover:bg-gray-50;
}

/* Custom animations */
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.animate-slide-in {
  animation: slideIn 0.3s ease;
}

.animate-slide-out {
  animation: slideOut 0.3s ease;
}
