'use client';

import React, { useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { redirect } from 'next/navigation';
import LandSearchDashboard from '@/components/LandSearchDashboard';

export default function DashboardPage() {
  const { data: session, status } = useSession();

  // Redirect if not authenticated or wrong account type
  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session) {
      redirect('/auth/signin?callbackUrl=/dashboard');
      return;
    }

    if (session.user.accountType !== 'land' && session.user.accountType !== 'data') {
      redirect('/auth/signin?error=access_denied');
      return;
    }
  }, [session, status]);

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!session || (session.user.accountType !== 'land' && session.user.accountType !== 'data')) {
    return null; // Will redirect
  }

  return <LandSearchDashboard />;
}
