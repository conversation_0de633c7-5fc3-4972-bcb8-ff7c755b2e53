import { NextRequest, NextResponse } from 'next/server';
import { PropertySearchRequest, PropertySearchResponse, Property } from '@/types/property';
import { isValidCoordinate } from '@/lib/utils';

const API_BASE_URL = 'https://propbolt.com';

/**
 * Validates and sanitizes property coordinates
 * @param property Property object to validate
 * @returns Property with validated coordinates or null if invalid
 */
function validatePropertyCoordinates(property: any): Property | null {
  // Ensure required fields exist
  if (!property || typeof property !== 'object') {
    return null;
  }

  // Convert coordinate fields to numbers and validate
  const latitude = typeof property.latitude === 'number' ? property.latitude : 
                  typeof property.lat === 'number' ? property.lat :
                  parseFloat(property.latitude || property.lat || '0');
  
  const longitude = typeof property.longitude === 'number' ? property.longitude :
                   typeof property.lng === 'number' ? property.lng :
                   parseFloat(property.longitude || property.lng || '0');

  // Validate coordinates
  if (!isValidCoordinate(latitude, longitude)) {
    console.warn(`Property ${property.id || 'unknown'} has invalid coordinates:`, {
      lat: latitude,
      lng: longitude,
      address: property.address
    });
    return null;
  }

  // Return sanitized property with validated coordinates
  return {
    id: property.id || 0,
    address: property.address || '',
    price: property.price || 0,
    size: property.size || '',
    zoning: property.zoning || '',
    latitude: latitude,
    longitude: longitude,
    description: property.description || '',
    habitability: property.habitability || '',
    proximity: property.proximity || '',
    chainLeasePotential: property.chainLeasePotential || property.chain_lease_potential || '',
    daysOnMarket: property.daysOnMarket || property.days_on_market || 0,
    pricePerSqFt: property.pricePerSqFt || property.price_per_sqft || 0,
    createdAt: property.createdAt || property.created_at || new Date().toISOString(),
    updatedAt: property.updatedAt || property.updated_at || new Date().toISOString(),
  };
}

export async function POST(request: NextRequest) {
  try {
    const body: PropertySearchRequest = await request.json();
    
    // Forward request to Go backend
    const response = await fetch(`${API_BASE_URL}/api/search`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      throw new Error(`Backend API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    // Validate and sanitize property coordinates
    const validatedProperties: Property[] = [];
    const invalidProperties: any[] = [];

    if (data.results && Array.isArray(data.results)) {
      data.results.forEach((property: any) => {
        const validatedProperty = validatePropertyCoordinates(property);
        if (validatedProperty) {
          validatedProperties.push(validatedProperty);
        } else {
          invalidProperties.push(property);
        }
      });
    }

    // Log invalid properties for debugging
    if (invalidProperties.length > 0) {
      console.warn(`Found ${invalidProperties.length} properties with invalid coordinates:`, 
        invalidProperties.map(p => ({ id: p.id, address: p.address, lat: p.latitude || p.lat, lng: p.longitude || p.lng }))
      );
    }

    const result: PropertySearchResponse = {
      results: validatedProperties,
      total: validatedProperties.length,
    };

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in search API route:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to search properties',
        message: error instanceof Error ? error.message : 'Unknown error',
        results: [],
        total: 0
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  // Handle GET requests for initial property loading
  try {
    const response = await fetch(`${API_BASE_URL}/api/search`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: '',
        filters: {}
      }),
    });

    if (!response.ok) {
      throw new Error(`Backend API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    // Validate and sanitize property coordinates
    const validatedProperties: Property[] = [];
    
    if (data.results && Array.isArray(data.results)) {
      data.results.forEach((property: any) => {
        const validatedProperty = validatePropertyCoordinates(property);
        if (validatedProperty) {
          validatedProperties.push(validatedProperty);
        }
      });
    }

    const result: PropertySearchResponse = {
      results: validatedProperties,
      total: validatedProperties.length,
    };

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in search API route (GET):', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to load properties',
        message: error instanceof Error ? error.message : 'Unknown error',
        results: [],
        total: 0
      },
      { status: 500 }
    );
  }
}
