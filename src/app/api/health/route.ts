import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Basic health check
    const healthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'PropBolt Frontend',
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
      checks: {
        database: 'unknown', // Will be updated when we add database health check
        apis: 'unknown'      // Will be updated when we add API health checks
      }
    };

    // Test database connection (basic check)
    try {
      if (process.env.DATABASE_URL) {
        healthStatus.checks.database = 'configured';
      }
    } catch (error) {
      healthStatus.checks.database = 'error';
    }

    // Test API endpoints (basic check)
    try {
      if (process.env.NEXT_PUBLIC_API_BASE_URL) {
        healthStatus.checks.apis = 'configured';
      }
    } catch (error) {
      healthStatus.checks.apis = 'error';
    }

    return NextResponse.json(healthStatus, { status: 200 });

  } catch (error) {
    console.error('Health check error:', error);
    
    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: 'Health check failed'
      },
      { status: 500 }
    );
  }
}

// Also handle HEAD requests for load balancer health checks
export async function HEAD(request: NextRequest) {
  return new NextResponse(null, { status: 200 });
}
