'use client';

import React from 'react';
import { Settings, User, Bell, Database, Key, Shield, Map } from 'lucide-react';
import { MapComparison } from '@/components/MapComparison';
import { ParameterComparison } from '@/components/ParameterComparison';

export default function SettingsPage() {
  return (
    <div className="p-8 space-y-8">
      {/* Page Header */}
      <div className="card">
        <div>
          <h1 className="text-3xl font-bold text-zillow-dark-gray mb-2">
            Settings
          </h1>
          <p className="text-gray-600">
            Manage your account and application preferences
          </p>
        </div>
      </div>

      {/* Settings Sections */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Account Settings */}
        <div className="card">
          <div className="flex items-center space-x-2 mb-4">
            <User size={20} className="text-zillow-blue" />
            <h3 className="text-lg font-semibold">Account Settings</h3>
          </div>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Display Name
              </label>
              <input
                type="text"
                defaultValue="Admin User"
                className="input-field"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email
              </label>
              <input
                type="email"
                defaultValue="<EMAIL>"
                className="input-field"
              />
            </div>
            <button className="btn-primary">Save Changes</button>
          </div>
        </div>

        {/* Notifications */}
        <div className="card">
          <div className="flex items-center space-x-2 mb-4">
            <Bell size={20} className="text-zillow-blue" />
            <h3 className="text-lg font-semibold">Notifications</h3>
          </div>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span>New property alerts</span>
              <input type="checkbox" defaultChecked className="rounded" />
            </div>
            <div className="flex items-center justify-between">
              <span>Price change notifications</span>
              <input type="checkbox" defaultChecked className="rounded" />
            </div>
            <div className="flex items-center justify-between">
              <span>Weekly market reports</span>
              <input type="checkbox" className="rounded" />
            </div>
          </div>
        </div>

        {/* API Configuration */}
        <div className="card">
          <div className="flex items-center space-x-2 mb-4">
            <Key size={20} className="text-zillow-blue" />
            <h3 className="text-lg font-semibold">API Configuration</h3>
          </div>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Mapbox Token
              </label>
              <input
                type="password"
                defaultValue="pk.eyJ1Ijoic2F2ZXVuZGVyY29udHJhY3QiLCJhIjoiY202anNoNWtvMDRpMjJqb2x1MWZ3ZGYxbCJ9.Bxl18yPka0BUSrGibMTbhg"
                className="input-field"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Real Estate API Key
              </label>
              <input
                type="password"
                defaultValue="AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914"
                className="input-field"
              />
            </div>
          </div>
        </div>

        {/* Database Settings */}
        <div className="card">
          <div className="flex items-center space-x-2 mb-4">
            <Database size={20} className="text-zillow-blue" />
            <h3 className="text-lg font-semibold">Database Settings</h3>
          </div>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span>Auto-sync properties</span>
              <input type="checkbox" defaultChecked className="rounded" />
            </div>
            <div className="flex items-center justify-between">
              <span>Cache search results</span>
              <input type="checkbox" defaultChecked className="rounded" />
            </div>
            <button className="btn-secondary">Clear Cache</button>
          </div>
        </div>
      </div>

      {/* Map Service Comparison */}
      <div className="card">
        <div className="flex items-center space-x-2 mb-6">
          <Map size={20} className="text-zillow-blue" />
          <h3 className="text-lg font-semibold">Map Service Configuration</h3>
        </div>
        <MapComparison />
      </div>
    </div>
  );
}
