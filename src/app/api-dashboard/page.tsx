'use client';

import { useState, useEffect } from 'react';
import { useSession, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Key, BarChart3, FileText, Settings, LogOut, Copy, Eye, EyeOff } from 'lucide-react';

interface APIKey {
  id: string;
  name: string;
  key: string;
  isActive: boolean;
  rateLimit: number;
  createdAt: string;
  lastUsed: string | null;
}

export default function APIDashboard() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [apiKeys, setApiKeys] = useState<APIKey[]>([]);
  const [showKeys, setShowKeys] = useState<{ [key: string]: boolean }>({});
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session) {
      router.push('/access');
      return;
    }

    // Check if user has 'data' account type for API access
    if (session.user.accountType !== 'data') {
      router.push('/access');
      return;
    }

    // Load API keys (mock data for now)
    setApiKeys([
      {
        id: '1',
        name: 'Production API Key',
        key: 'pb_live_1234567890abcdef',
        isActive: true,
        rateLimit: 1000,
        createdAt: '2025-06-01',
        lastUsed: '2025-06-10'
      },
      {
        id: '2',
        name: 'Development API Key',
        key: 'pb_test_abcdef1234567890',
        isActive: true,
        rateLimit: 500,
        createdAt: '2025-06-05',
        lastUsed: null
      }
    ]);
    setIsLoading(false);
  }, [session, status, router]);

  const handleSignOut = () => {
    signOut({ callbackUrl: '/access' });
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  const toggleKeyVisibility = (keyId: string) => {
    setShowKeys(prev => ({
      ...prev,
      [keyId]: !prev[keyId]
    }));
  };

  if (status === 'loading' || isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Key className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-xl font-semibold text-gray-900">PropBolt API Dashboard</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">Welcome, {session?.user?.name}</span>
              <button
                onClick={handleSignOut}
                className="flex items-center text-sm text-gray-600 hover:text-gray-900"
              >
                <LogOut className="h-4 w-4 mr-1" />
                Sign Out
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <Key className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active API Keys</p>
                <p className="text-2xl font-bold text-gray-900">{apiKeys.filter(k => k.isActive).length}</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <BarChart3 className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Monthly Requests</p>
                <p className="text-2xl font-bold text-gray-900">12,543</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <Settings className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Rate Limit</p>
                <p className="text-2xl font-bold text-gray-900">1,000/hr</p>
              </div>
            </div>
          </div>
        </div>

        {/* API Keys Section */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">API Keys</h2>
            <p className="text-sm text-gray-600">Manage your API keys for accessing PropBolt services</p>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {apiKeys.map((apiKey) => (
                <div key={apiKey.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h3 className="text-sm font-medium text-gray-900">{apiKey.name}</h3>
                      <div className="mt-2 flex items-center space-x-4">
                        <div className="flex items-center">
                          <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono">
                            {showKeys[apiKey.id] ? apiKey.key : '••••••••••••••••'}
                          </code>
                          <button
                            onClick={() => toggleKeyVisibility(apiKey.id)}
                            className="ml-2 text-gray-400 hover:text-gray-600"
                          >
                            {showKeys[apiKey.id] ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                          </button>
                          <button
                            onClick={() => copyToClipboard(apiKey.key)}
                            className="ml-2 text-gray-400 hover:text-gray-600"
                          >
                            <Copy className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                      <div className="mt-2 text-xs text-gray-500">
                        Created: {apiKey.createdAt} | Last used: {apiKey.lastUsed || 'Never'} | Rate limit: {apiKey.rateLimit}/hour
                      </div>
                    </div>
                    <div className="flex items-center">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        apiKey.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {apiKey.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* API Documentation Link */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-center">
            <FileText className="h-6 w-6 text-blue-600 mr-3" />
            <div>
              <h3 className="text-lg font-medium text-blue-900">API Documentation</h3>
              <p className="text-blue-700 mt-1">
                Learn how to integrate with our property data API and explore available endpoints.
              </p>
              <a
                href="/api-docs"
                className="inline-flex items-center mt-3 text-blue-600 hover:text-blue-700 font-medium"
              >
                View Documentation →
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
