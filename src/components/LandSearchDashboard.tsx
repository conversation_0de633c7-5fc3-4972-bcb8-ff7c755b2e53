'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { 
  Search, 
  Filter, 
  MapPin, 
  DollarSign, 
  Ruler, 
  Building, 
  Zap,
  AlertTriangle,
  TrendingUp,
  Eye,
  Heart,
  MoreHorizontal
} from 'lucide-react';
import { GoogleMapsComponent } from './GoogleMapsComponent';
import PropertyAnalysisModal from './PropertyAnalysisModal';

interface Property {
  id: string;
  address: string;
  price: number;
  acres?: number;
  zoning?: string;
  latitude: number;
  longitude: number;
  description?: string;
  utilities?: string[];
  source: 'RealEstateAPI' | 'PropBolt';
  details?: any;
}

interface SearchFilters {
  location: string;
  minPrice?: number;
  maxPrice?: number;
  minAcres?: number;
  maxAcres?: number;
  zoning?: string[];
  utilities?: string[];
}

export default function LandSearchDashboard() {
  const { data: session } = useSession();
  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedProperty, setSelectedProperty] = useState<Property | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [showAnalysisModal, setShowAnalysisModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchStats, setSearchStats] = useState<any>(null);

  const [filters, setFilters] = useState<SearchFilters>({
    location: 'Daytona Beach, FL',
    minPrice: undefined,
    maxPrice: undefined,
    minAcres: undefined,
    maxAcres: undefined,
    zoning: [],
    utilities: []
  });

  // Search for properties
  const searchProperties = async (page = 1) => {
    setLoading(true);
    try {
      const response = await fetch('/api/land-search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...filters,
          page,
          limit: 10
        }),
      });

      if (!response.ok) {
        throw new Error('Search failed');
      }

      const data = await response.json();
      setProperties(data.results || []);
      setTotalPages(data.pagination?.totalPages || 1);
      setSearchStats(data.sources);
    } catch (error) {
      console.error('Search error:', error);
    } finally {
      setLoading(false);
    }
  };

  // Initial search
  useEffect(() => {
    searchProperties();
  }, []);

  // Handle search
  const handleSearch = () => {
    setCurrentPage(1);
    searchProperties(1);
  };

  // Handle property click
  const handlePropertyClick = (property: Property) => {
    setSelectedProperty(property);
  };

  // Format price
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  // Format acres
  const formatAcres = (acres?: number) => {
    if (!acres) return 'N/A';
    return `${acres.toFixed(2)} acres`;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Vacant Land Search
              </h1>
              <p className="text-sm text-gray-600">
                Professional land analysis powered by dual API integration
              </p>
            </div>
            <div className="flex items-center space-x-4">
              {session?.user && (
                <div className="text-sm text-gray-600">
                  Welcome, {session.user.name} ({session.user.accountType})
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          
          {/* Search and Filters */}
          <div className="lg:col-span-1 space-y-6">
            
            {/* Search Box */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-semibold mb-4">Search Location</h2>
              <div className="space-y-4">
                <div>
                  <input
                    type="text"
                    value={filters.location}
                    onChange={(e) => setFilters({ ...filters, location: e.target.value })}
                    placeholder="Enter city, state, or address"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className="flex items-center text-sm text-blue-600 hover:text-blue-800"
                >
                  <Filter className="h-4 w-4 mr-1" />
                  Advanced Filters
                </button>

                {showFilters && (
                  <div className="space-y-4 pt-4 border-t">
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Min Price
                        </label>
                        <input
                          type="number"
                          value={filters.minPrice || ''}
                          onChange={(e) => setFilters({ ...filters, minPrice: e.target.value ? parseInt(e.target.value) : undefined })}
                          placeholder="$0"
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Max Price
                        </label>
                        <input
                          type="number"
                          value={filters.maxPrice || ''}
                          onChange={(e) => setFilters({ ...filters, maxPrice: e.target.value ? parseInt(e.target.value) : undefined })}
                          placeholder="$1,000,000"
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Min Acres
                        </label>
                        <input
                          type="number"
                          step="0.1"
                          value={filters.minAcres || ''}
                          onChange={(e) => setFilters({ ...filters, minAcres: e.target.value ? parseFloat(e.target.value) : undefined })}
                          placeholder="0.1"
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Max Acres
                        </label>
                        <input
                          type="number"
                          step="0.1"
                          value={filters.maxAcres || ''}
                          onChange={(e) => setFilters({ ...filters, maxAcres: e.target.value ? parseFloat(e.target.value) : undefined })}
                          placeholder="100"
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                      </div>
                    </div>
                  </div>
                )}

                <button
                  onClick={handleSearch}
                  disabled={loading}
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 flex items-center justify-center"
                >
                  {loading ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  ) : (
                    <>
                      <Search className="h-4 w-4 mr-2" />
                      Search Properties
                    </>
                  )}
                </button>
              </div>
            </div>

            {/* Search Stats */}
            {searchStats && (
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold mb-4">Search Results</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>RealEstateAPI:</span>
                    <span className="font-medium">{searchStats.realEstateAPI}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>PropBolt API:</span>
                    <span className="font-medium">{searchStats.propBolt}</span>
                  </div>
                  <div className="flex justify-between border-t pt-2">
                    <span>Total Combined:</span>
                    <span className="font-bold">{searchStats.combined}</span>
                  </div>
                </div>
              </div>
            )}

            {/* Property List */}
            <div className="bg-white rounded-lg shadow">
              <div className="p-6 border-b">
                <h3 className="text-lg font-semibold">Properties</h3>
              </div>
              <div className="max-h-96 overflow-y-auto">
                {properties.map((property) => (
                  <div
                    key={property.id}
                    onClick={() => handlePropertyClick(property)}
                    className="p-4 border-b hover:bg-gray-50 cursor-pointer"
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900 text-sm">
                          {property.address}
                        </h4>
                        <div className="flex items-center space-x-4 mt-2 text-xs text-gray-600">
                          <span className="flex items-center">
                            <DollarSign className="h-3 w-3 mr-1" />
                            {formatPrice(property.price)}
                          </span>
                          {property.acres && (
                            <span className="flex items-center">
                              <Ruler className="h-3 w-3 mr-1" />
                              {formatAcres(property.acres)}
                            </span>
                          )}
                          {property.zoning && (
                            <span className="flex items-center">
                              <Building className="h-3 w-3 mr-1" />
                              {property.zoning}
                            </span>
                          )}
                        </div>
                        <div className="mt-1">
                          <span className={`inline-block px-2 py-1 text-xs rounded-full ${
                            property.source === 'RealEstateAPI' 
                              ? 'bg-blue-100 text-blue-800' 
                              : 'bg-green-100 text-green-800'
                          }`}>
                            {property.source}
                          </span>
                        </div>
                      </div>
                      <button className="text-gray-400 hover:text-gray-600">
                        <MoreHorizontal className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
              
              {/* Pagination */}
              {totalPages > 1 && (
                <div className="p-4 border-t flex justify-between items-center">
                  <button
                    onClick={() => {
                      const newPage = currentPage - 1;
                      setCurrentPage(newPage);
                      searchProperties(newPage);
                    }}
                    disabled={currentPage === 1}
                    className="px-3 py-1 text-sm border rounded disabled:opacity-50"
                  >
                    Previous
                  </button>
                  <span className="text-sm text-gray-600">
                    Page {currentPage} of {totalPages}
                  </span>
                  <button
                    onClick={() => {
                      const newPage = currentPage + 1;
                      setCurrentPage(newPage);
                      searchProperties(newPage);
                    }}
                    disabled={currentPage === totalPages}
                    className="px-3 py-1 text-sm border rounded disabled:opacity-50"
                  >
                    Next
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Map and Property Details */}
          <div className="lg:col-span-2 space-y-6">
            
            {/* Map */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-4">Property Locations</h3>
              <div className="h-96">
                <GoogleMapsComponent
                  center={{ lat: 29.2108, lng: -81.0228 }}
                  zoom={12}
                  properties={properties.map(p => ({
                    id: p.id,
                    lat: p.latitude,
                    lng: p.longitude,
                    address: p.address,
                    price: p.price,
                    acres: p.acres,
                    zoning: p.zoning
                  }))}
                  onPropertyClick={handlePropertyClick}
                  className="w-full h-full"
                />
              </div>
            </div>

            {/* Selected Property Details */}
            {selectedProperty && (
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold mb-4">Property Details</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">
                      {selectedProperty.address}
                    </h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Price:</span>
                        <span className="font-medium">{formatPrice(selectedProperty.price)}</span>
                      </div>
                      {selectedProperty.acres && (
                        <div className="flex justify-between">
                          <span>Size:</span>
                          <span className="font-medium">{formatAcres(selectedProperty.acres)}</span>
                        </div>
                      )}
                      {selectedProperty.zoning && (
                        <div className="flex justify-between">
                          <span>Zoning:</span>
                          <span className="font-medium">{selectedProperty.zoning}</span>
                        </div>
                      )}
                      <div className="flex justify-between">
                        <span>Source:</span>
                        <span className={`font-medium ${
                          selectedProperty.source === 'RealEstateAPI' ? 'text-blue-600' : 'text-green-600'
                        }`}>
                          {selectedProperty.source}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h5 className="font-medium text-gray-900 mb-2">Quick Actions</h5>
                    <div className="space-y-2">
                      <button
                        onClick={() => setShowAnalysisModal(true)}
                        className="w-full text-left px-3 py-2 text-sm border rounded hover:bg-gray-50 flex items-center"
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        View Full Analysis
                      </button>
                      <button className="w-full text-left px-3 py-2 text-sm border rounded hover:bg-gray-50 flex items-center">
                        <Heart className="h-4 w-4 mr-2" />
                        Add to Watchlist
                      </button>
                      <button className="w-full text-left px-3 py-2 text-sm border rounded hover:bg-gray-50 flex items-center">
                        <TrendingUp className="h-4 w-4 mr-2" />
                        Investment Analysis
                      </button>
                    </div>
                  </div>
                </div>
                {selectedProperty.description && (
                  <div className="mt-4 pt-4 border-t">
                    <h5 className="font-medium text-gray-900 mb-2">Description</h5>
                    <p className="text-sm text-gray-600">{selectedProperty.description}</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Property Analysis Modal */}
      {selectedProperty && (
        <PropertyAnalysisModal
          property={selectedProperty}
          isOpen={showAnalysisModal}
          onClose={() => setShowAnalysisModal(false)}
        />
      )}
    </div>
  );
}
