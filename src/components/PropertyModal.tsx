'use client';

import React, { useEffect } from 'react';
import { X, MapPin, DollarSign, Calendar, Ruler, Home, Zap } from 'lucide-react';
import { formatPrice, getZoningColor, getChainPotentialColor, calculateProximityToBeach } from '@/lib/utils';
import type { Property } from '@/types/property';

interface PropertyModalProps {
  property: Property;
  isOpen: boolean;
  onClose: () => void;
  onNext?: () => void;
  onPrevious?: () => void;
  currentIndex?: number;
  totalCount?: number;
}

export function PropertyModal({ 
  property, 
  isOpen, 
  onClose, 
  onNext, 
  onPrevious, 
  currentIndex, 
  totalCount 
}: PropertyModalProps) {
  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  // Handle arrow keys for navigation
  useEffect(() => {
    const handleArrowKeys = (e: KeyboardEvent) => {
      if (e.key === 'ArrowLeft' && onPrevious) {
        onPrevious();
      } else if (e.key === 'ArrowRight' && onNext) {
        onNext();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleArrowKeys);
    }

    return () => {
      document.removeEventListener('keydown', handleArrowKeys);
    };
  }, [isOpen, onNext, onPrevious]);

  if (!isOpen) return null;

  const beachDistance = calculateProximityToBeach(property.latitude, property.longitude);
  const pricePerSqFt = property.pricePerSqFt > 0 ? `$${property.pricePerSqFt.toFixed(2)}/sq ft` : 'N/A';

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop with blur */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal Content */}
      <div className="relative bg-white rounded-lg shadow-2xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h2 className="text-2xl font-bold text-zillow-blue">
              {formatPrice(property.price)}
            </h2>
            {currentIndex !== undefined && totalCount !== undefined && (
              <span className="text-sm text-gray-500">
                {currentIndex + 1} of {totalCount}
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Navigation buttons */}
            {onPrevious && (
              <button
                onClick={onPrevious}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                disabled={currentIndex === 0}
              >
                ←
              </button>
            )}
            {onNext && (
              <button
                onClick={onNext}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                disabled={currentIndex === (totalCount || 1) - 1}
              >
                →
              </button>
            )}
            
            {/* Close button */}
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X size={24} />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Address */}
          <div className="flex items-center space-x-2 mb-6">
            <MapPin className="text-zillow-blue" size={20} />
            <h3 className="text-xl font-semibold">{property.address}</h3>
          </div>

          {/* Key Details Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <Ruler className="text-zillow-blue" size={16} />
                <span className="font-medium">Property Size</span>
              </div>
              <p className="text-lg">{property.size}</p>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <DollarSign className="text-zillow-blue" size={16} />
                <span className="font-medium">Price per Sq Ft</span>
              </div>
              <p className="text-lg">{pricePerSqFt}</p>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <Calendar className="text-zillow-blue" size={16} />
                <span className="font-medium">Days on Market</span>
              </div>
              <p className="text-lg">{property.daysOnMarket} days</p>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <Home className="text-zillow-blue" size={16} />
                <span className="font-medium">Habitability</span>
              </div>
              <p className="text-lg">{property.habitability}</p>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <MapPin className="text-zillow-blue" size={16} />
                <span className="font-medium">Beach Distance</span>
              </div>
              <p className="text-lg">{beachDistance}</p>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <Zap className="text-zillow-blue" size={16} />
                <span className="font-medium">Chain Potential</span>
              </div>
              <p className="text-lg">{property.chainLeasePotential}</p>
            </div>
          </div>

          {/* Tags */}
          <div className="flex flex-wrap gap-3 mb-6">
            <span className={`px-3 py-2 rounded-full text-sm font-medium ${getZoningColor(property.zoning)}`}>
              {property.zoning} Zoning
            </span>
            <span className={`px-3 py-2 rounded-full text-sm font-medium ${getChainPotentialColor(property.chainLeasePotential)}`}>
              Chain: {property.chainLeasePotential}
            </span>
          </div>

          {/* Proximity Details */}
          <div className="bg-blue-50 p-4 rounded-lg mb-6">
            <h4 className="font-semibold mb-2">Location & Proximity</h4>
            <p className="text-gray-700">{property.proximity}</p>
          </div>

          {/* Description */}
          {property.description && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-semibold mb-2">Property Description</h4>
              <p className="text-gray-700">{property.description}</p>
            </div>
          )}

          {/* Coordinates (for debugging) */}
          <div className="mt-6 text-xs text-gray-400">
            Coordinates: {property.latitude.toFixed(6)}, {property.longitude.toFixed(6)}
          </div>
        </div>
      </div>
    </div>
  );
}
