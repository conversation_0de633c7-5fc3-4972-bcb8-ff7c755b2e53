'use client';

import React, { useState } from 'react';
import { CheckCircle, AlertCircle, Plus, Settings, MapPin, Filter } from 'lucide-react';

interface ParameterStatus {
  name: string;
  current: string | boolean | number;
  status: 'correct' | 'needs_improvement' | 'missing';
  recommendation?: string;
  impact: 'high' | 'medium' | 'low';
}

export function ParameterComparison() {
  const [activeTab, setActiveTab] = useState<'current' | 'recommended'>('current');

  const currentParameters: ParameterStatus[] = [
    {
      name: 'isLotLand',
      current: true,
      status: 'correct',
      impact: 'high'
    },
    {
      name: 'isAllHomes',
      current: false,
      status: 'correct',
      impact: 'high'
    },
    {
      name: 'Property Type Filters',
      current: 'All set to false (townhouse, condo, apartment, etc.)',
      status: 'correct',
      impact: 'high'
    },
    {
      name: 'School District Filters',
      current: 'All set to false',
      status: 'correct',
      impact: 'medium'
    },
    {
      name: 'Geographic Scope',
      current: 'Daytona Beach, FL only',
      status: 'needs_improvement',
      recommendation: 'Add support for multiple locations',
      impact: 'high'
    },
    {
      name: 'Price Filters',
      current: 'Basic min/max price',
      status: 'needs_improvement',
      recommendation: 'Add price per acre, financing options',
      impact: 'medium'
    },
    {
      name: 'Size Filters',
      current: 'Not implemented',
      status: 'missing',
      recommendation: 'Add min/max acres filtering',
      impact: 'high'
    },
    {
      name: 'Utilities Filter',
      current: 'Not implemented',
      status: 'missing',
      recommendation: 'Add utilities availability filter',
      impact: 'medium'
    },
    {
      name: 'Road Access Filter',
      current: 'Not implemented',
      status: 'missing',
      recommendation: 'Add paved road access filter',
      impact: 'medium'
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'correct':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'needs_improvement':
        return <AlertCircle className="h-5 w-5 text-yellow-600" />;
      case 'missing':
        return <Plus className="h-5 w-5 text-red-600" />;
      default:
        return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'correct':
        return 'bg-green-50 border-green-200';
      case 'needs_improvement':
        return 'bg-yellow-50 border-yellow-200';
      case 'missing':
        return 'bg-red-50 border-red-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  const getImpactBadge = (impact: string) => {
    const colors = {
      high: 'bg-red-100 text-red-800',
      medium: 'bg-yellow-100 text-yellow-800',
      low: 'bg-green-100 text-green-800'
    };
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colors[impact as keyof typeof colors]}`}>
        {impact.toUpperCase()}
      </span>
    );
  };

  const correctCount = currentParameters.filter(p => p.status === 'correct').length;
  const needsImprovementCount = currentParameters.filter(p => p.status === 'needs_improvement').length;
  const missingCount = currentParameters.filter(p => p.status === 'missing').length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center space-x-3 mb-4">
          <Settings className="h-6 w-6 text-blue-600" />
          <h2 className="text-xl font-semibold text-gray-900">Land Search Parameters Analysis</h2>
        </div>
        
        {/* Status Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center">
              <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
              <div>
                <p className="text-sm font-medium text-green-900">Correctly Configured</p>
                <p className="text-2xl font-bold text-green-600">{correctCount}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center">
              <AlertCircle className="h-5 w-5 text-yellow-600 mr-2" />
              <div>
                <p className="text-sm font-medium text-yellow-900">Needs Improvement</p>
                <p className="text-2xl font-bold text-yellow-600">{needsImprovementCount}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center">
              <Plus className="h-5 w-5 text-red-600 mr-2" />
              <div>
                <p className="text-sm font-medium text-red-900">Missing Features</p>
                <p className="text-2xl font-bold text-red-600">{missingCount}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-semibold text-blue-900 mb-2">✅ Overall Assessment</h3>
          <p className="text-blue-800 text-sm">
            Your core land search parameters are <strong>correctly configured</strong> for vacant land search. 
            The main focus should be on adding enhanced filtering options and expanding geographic support.
          </p>
        </div>
      </div>

      {/* Parameter Details */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Parameter Details</h3>
        </div>
        
        <div className="p-6">
          <div className="space-y-4">
            {currentParameters.map((param, index) => (
              <div key={index} className={`border rounded-lg p-4 ${getStatusColor(param.status)}`}>
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    {getStatusIcon(param.status)}
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{param.name}</h4>
                      <p className="text-sm text-gray-600 mt-1">
                        Current: <code className="bg-gray-100 px-2 py-1 rounded text-xs">{param.current.toString()}</code>
                      </p>
                      {param.recommendation && (
                        <p className="text-sm text-gray-700 mt-2">
                          <strong>Recommendation:</strong> {param.recommendation}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getImpactBadge(param.impact)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Recommended Enhancements */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <Filter className="h-5 w-5 text-green-600" />
            <h3 className="text-lg font-semibold text-gray-900">Recommended Enhanced Parameters</h3>
          </div>
        </div>
        
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-gray-900 mb-3">Geographic Enhancements</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Dynamic location selection (beyond Daytona Beach)</li>
                <li>• Radius-based search (5, 10, 25 mile radius)</li>
                <li>• Custom coordinate boundary input</li>
                <li>• Multi-city search support</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold text-gray-900 mb-3">Property Filters</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Lot size range (min/max acres)</li>
                <li>• Price per acre calculations</li>
                <li>• Utilities availability (water, sewer, electric)</li>
                <li>• Road access type (paved, gravel, private)</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold text-gray-900 mb-3">Investment Criteria</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Chain lease potential scoring</li>
                <li>• Development feasibility assessment</li>
                <li>• Proximity to amenities (beach, city center)</li>
                <li>• Market timing (days on market filters)</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold text-gray-900 mb-3">Data Integration</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Google Maps geocoding integration</li>
                <li>• Real-time property status validation</li>
                <li>• Enhanced zoning data from municipal APIs</li>
                <li>• Environmental constraints checking</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Implementation Priority */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-6">
        <div className="flex items-start space-x-3">
          <MapPin className="h-6 w-6 text-green-600 mt-1" />
          <div>
            <h3 className="font-semibold text-gray-900 mb-2">Implementation Priority</h3>
            <div className="space-y-2 text-sm text-gray-700">
              <p><strong>Phase 1 (High Priority):</strong> Google Maps integration, lot size filters, multi-location support</p>
              <p><strong>Phase 2 (Medium Priority):</strong> Utilities filters, enhanced proximity calculations, price per acre</p>
              <p><strong>Phase 3 (Low Priority):</strong> Environmental constraints, municipal API integration, advanced scoring</p>
            </div>
            <div className="mt-4 p-3 bg-white rounded border border-green-200">
              <p className="text-sm text-green-800">
                <strong>✅ Conclusion:</strong> Your current parameters are fundamentally correct for vacant land search. 
                Focus on enhancing rather than replacing the existing implementation.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
