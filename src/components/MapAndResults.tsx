'use client';

import React, { useState } from 'react';
import { MapComponent } from './MapComponent';
import { PropertyList } from './PropertyList';
import { MapErrorBoundary } from './MapErrorBoundary';
import { PropertyModal } from './PropertyModal';
import { useModalNavigation } from '@/hooks/usePagination';
import type { Property, PropertySearchFilters } from '@/types/property';

interface MapAndResultsProps {
  properties: Property[];
  searchQuery: string;
  filters: PropertySearchFilters;
}

export function MapAndResults({ properties, searchQuery, filters }: MapAndResultsProps) {
  const [selectedProperty, setSelectedProperty] = useState<Property | null>(null);
  const [isMapModalOpen, setIsMapModalOpen] = useState(false);

  // Modal navigation for map-triggered modals
  const {
    currentIndex: mapModalIndex,
    currentItem: mapModalProperty,
    hasNext: mapModalHasNext,
    hasPrevious: mapModalHasPrevious,
    goToNext: mapModalNext,
    goToPrevious: mapModalPrevious,
    goToIndex: mapModalGoToIndex,
    totalCount: mapModalTotalCount
  } = useModalNavigation(properties);

  const handlePropertySelect = (property: Property) => {
    setSelectedProperty(property);
  };

  const handleMapPropertyClick = (propertyId: number) => {
    const property = properties.find(p => p.id === propertyId);
    if (property) {
      setSelectedProperty(property);
    }
  };

  const handleMapPropertyModalOpen = (property: Property) => {
    const index = properties.findIndex(p => p.id === property.id);
    if (index !== -1) {
      mapModalGoToIndex(index);
      setIsMapModalOpen(true);
    }
  };

  const handleCloseMapModal = () => {
    setIsMapModalOpen(false);
  };

  return (
    <>
      <section className="grid grid-cols-1 lg:grid-cols-3 gap-8 h-[600px]">
        {/* Map Container */}
        <div className="lg:col-span-2 card p-0 overflow-hidden">
          <MapErrorBoundary>
            <MapComponent
              properties={properties}
              selectedProperty={selectedProperty}
              onPropertyClick={handleMapPropertyClick}
              onPropertyModalOpen={handleMapPropertyModalOpen}
            />
          </MapErrorBoundary>
        </div>

        {/* Results Panel */}
        <div className="card overflow-hidden flex flex-col">
          <div className="flex items-center justify-between p-6 border-b border-zillow-border">
            <h3 className="text-lg font-semibold">Search Results</h3>
            <span className="text-sm text-gray-600">
              {properties.length} properties
            </span>
          </div>

          <div className="flex-1 overflow-y-auto">
            <PropertyList
              properties={properties}
              selectedProperty={selectedProperty}
              onPropertySelect={handlePropertySelect}
            />
          </div>
        </div>
      </section>

      {/* Map Modal */}
      {mapModalProperty && (
        <PropertyModal
          property={mapModalProperty}
          isOpen={isMapModalOpen}
          onClose={handleCloseMapModal}
          onNext={mapModalHasNext ? mapModalNext : undefined}
          onPrevious={mapModalHasPrevious ? mapModalPrevious : undefined}
          currentIndex={mapModalIndex}
          totalCount={mapModalTotalCount}
        />
      )}
    </>
  );
}
