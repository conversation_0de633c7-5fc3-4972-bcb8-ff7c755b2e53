'use client';

import React, { useState } from 'react';
import { formatPrice, getZoningColor, getChainPotentialColor } from '@/lib/utils';
import { PropertyModal } from './PropertyModal';
import { usePagination, useModalNavigation } from '@/hooks/usePagination';
import type { Property } from '@/types/property';

interface PropertyListProps {
  properties: Property[];
  selectedProperty: Property | null;
  onPropertySelect: (property: Property) => void;
}

export function PropertyList({ properties, selectedProperty, onPropertySelect }: PropertyListProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Pagination: 10 items per page
  const {
    currentItems,
    currentPage,
    totalPages,
    nextPage,
    previousPage,
    hasNextPage,
    hasPreviousPage,
    startIndex,
    endIndex
  } = usePagination({ items: properties, itemsPerPage: 10 });

  // Modal navigation within current page
  const {
    currentIndex,
    currentItem,
    hasNext,
    hasPrevious,
    goToNext,
    goToPrevious,
    goToIndex,
    totalCount
  } = useModalNavigation(currentItems);

  const handlePropertyClick = (property: Property, index: number) => {
    onPropertySelect(property);
    goToIndex(index);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  if (properties.length === 0) {
    return (
      <div className="p-6 text-center text-gray-500">
        <p>No properties found matching your criteria.</p>
        <p className="text-sm mt-2">Try adjusting your search filters.</p>
      </div>
    );
  }

  return (
    <>
      <div className="p-6">
        {/* Pagination Info */}
        <div className="flex items-center justify-between mb-4 text-sm text-gray-600">
          <span>
            Showing {startIndex + 1}-{endIndex} of {properties.length} properties
          </span>
          <span>
            Page {currentPage} of {totalPages}
          </span>
        </div>

        {/* Property Cards */}
        <div className="space-y-4 mb-6">
          {currentItems.map((property, index) => (
            <div
              key={property.id}
              onClick={() => handlePropertyClick(property, index)}
              className={`property-card cursor-pointer hover:shadow-lg transition-shadow ${
                selectedProperty?.id === property.id
                  ? 'border-zillow-blue bg-zillow-blue-light'
                  : ''
              }`}
            >
              {/* Price */}
              <div className="text-xl font-bold text-zillow-blue mb-2">
                {formatPrice(property.price)}
              </div>

              {/* Address */}
              <div className="font-medium mb-3">
                {property.address}
              </div>

              {/* Property Details Grid */}
              <div className="grid grid-cols-2 gap-2 text-sm text-gray-600 mb-3">
                <div>
                  <strong>Size:</strong> {property.size}
                </div>
                <div>
                  <strong>$/sq ft:</strong> ${property.pricePerSqFt}
                </div>
                <div>
                  <strong>Habitability:</strong> {property.habitability}
                </div>
                <div>
                  <strong>Days on Market:</strong> {property.daysOnMarket}
                </div>
                <div className="col-span-2">
                  <strong>Proximity:</strong> {property.proximity}
                </div>
              </div>

              {/* Tags */}
              <div className="flex flex-wrap gap-2">
                <span className={`px-2 py-1 rounded text-xs font-medium ${getZoningColor(property.zoning)}`}>
                  {property.zoning}
                </span>
                <span className={`px-2 py-1 rounded text-xs font-medium ${getChainPotentialColor(property.chainLeasePotential)}`}>
                  Chain: {property.chainLeasePotential}
                </span>
              </div>
            </div>
          ))}
        </div>

        {/* Pagination Controls */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between">
            <button
              onClick={previousPage}
              disabled={!hasPreviousPage}
              className={`px-4 py-2 rounded-lg border ${
                hasPreviousPage
                  ? 'border-zillow-blue text-zillow-blue hover:bg-zillow-blue hover:text-white'
                  : 'border-gray-300 text-gray-400 cursor-not-allowed'
              } transition-colors`}
            >
              Previous
            </button>

            <span className="text-sm text-gray-600">
              Page {currentPage} of {totalPages}
            </span>

            <button
              onClick={nextPage}
              disabled={!hasNextPage}
              className={`px-4 py-2 rounded-lg border ${
                hasNextPage
                  ? 'border-zillow-blue text-zillow-blue hover:bg-zillow-blue hover:text-white'
                  : 'border-gray-300 text-gray-400 cursor-not-allowed'
              } transition-colors`}
            >
              Next
            </button>
          </div>
        )}
      </div>

      {/* Property Modal */}
      {currentItem && (
        <PropertyModal
          property={currentItem}
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          onNext={hasNext ? goToNext : undefined}
          onPrevious={hasPrevious ? goToPrevious : undefined}
          currentIndex={currentIndex}
          totalCount={totalCount}
        />
      )}
    </>
  );
}
