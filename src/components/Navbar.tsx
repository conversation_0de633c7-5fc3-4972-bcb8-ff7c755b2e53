'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useSession, signOut } from 'next-auth/react';
import {
  BarChart3,
  Search,
  Heart,
  TrendingUp,
  FileText,
  Settings,
  Menu,
  X,
  LogOut,
  User,
} from 'lucide-react';

const navItems = [
  {
    href: '/',
    label: 'Dashboard',
    icon: BarChart3,
  },
  {
    href: '/search',
    label: 'Land Search',
    icon: Search,
  },
  {
    href: '/watchlist',
    label: 'Watch List',
    icon: Heart,
  },
  {
    href: '/analytics',
    label: 'Market Analytics',
    icon: TrendingUp,
  },
  {
    href: '/reports',
    label: 'Reports',
    icon: FileText,
  },
  {
    href: '/settings',
    label: 'Settings',
    icon: Settings,
  },
];

export function Navbar() {
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { data: session } = useSession();

  const handleSignOut = async () => {
    try {
      // Redirect to appropriate login page based on account type
      const callbackUrl = session?.user?.accountType === 'data' ? '/access' : '/land';
      await signOut({
        callbackUrl,
        redirect: true
      });
    } catch (error) {
      console.error('Error signing out:', error);
      // Force redirect to land search login page if signOut fails
      window.location.href = '/land';
    }
  };

  return (
    <>
      {/* Mobile menu button */}
      <button
        onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        className="lg:hidden fixed top-4 left-4 z-50 p-2 bg-zillow-blue text-white rounded-lg shadow-lg"
      >
        {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
      </button>

      {/* Overlay for mobile */}
      {isMobileMenuOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}

      {/* Navbar */}
      <nav
        className={`
          fixed left-0 top-0 h-full w-80 bg-gradient-to-b from-zillow-blue to-zillow-blue-dark
          text-white z-40 shadow-zillow-lg transition-transform duration-300 ease-in-out
          ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
        `}
      >
        {/* Header */}
        <div className="p-8 border-b border-white/10">
          <div className="text-2xl font-bold mb-2">Vacant Land Search</div>
          <div className="text-sm opacity-80">Daytona Beach, FL</div>
        </div>

        {/* Navigation Items */}
        <div className="py-4">
          {navItems.map((item) => {
            const Icon = item.icon;
            const isActive = pathname === item.href;

            return (
              <Link
                key={item.href}
                href={item.href}
                className={`navbar-item ${isActive ? 'active' : ''}`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <Icon size={20} className="mr-3" />
                {item.label}
              </Link>
            );
          })}
        </div>

        {/* User Info & Sign Out */}
        <div className="absolute bottom-0 left-0 right-0 p-6 border-t border-white/10">
          {session?.user && (
            <div className="mb-4">
              <div className="flex items-center text-white/90 mb-3">
                <User className="h-4 w-4 mr-2" />
                <div>
                  <div className="text-sm font-medium">{session.user.name}</div>
                  <div className="text-xs opacity-70">{session.user.email}</div>
                </div>
              </div>
              <button
                onClick={handleSignOut}
                className="flex items-center text-white/80 hover:text-white text-sm transition-colors"
              >
                <LogOut className="h-4 w-4 mr-2" />
                Sign Out
              </button>
            </div>
          )}
          <div className="text-xs opacity-60">
            © 2024 Vacant Land Search
          </div>
          <div className="text-xs opacity-60 mt-1">
            Powered by Real Estate API
          </div>
        </div>
      </nav>
    </>
  );
}
