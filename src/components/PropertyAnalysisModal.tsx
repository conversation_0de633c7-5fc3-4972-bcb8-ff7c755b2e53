'use client';

import React, { useState, useEffect } from 'react';
import { 
  X, 
  DollarSign, 
  Ruler, 
  Building, 
  AlertTriangle, 
  TrendingUp, 
  MapPin, 
  FileText,
  Users,
  Zap,
  Shield,
  Eye,
  Download
} from 'lucide-react';

interface Property {
  id: string;
  address: string;
  price: number;
  acres?: number;
  zoning?: string;
  latitude: number;
  longitude: number;
  description?: string;
  source: 'RealEstateAPI' | 'PropBolt';
}

interface PropertyAnalysisModalProps {
  property: Property;
  isOpen: boolean;
  onClose: () => void;
}

interface PropertyAnalysis {
  basicInfo: any;
  comparables: any[];
  liens: any[];
  ownerInfo: any;
  zoning: any;
  utilities: any;
  investment: any;
  risks: any[];
}

export default function PropertyAnalysisModal({ property, isOpen, onClose }: PropertyAnalysisModalProps) {
  const [analysis, setAnalysis] = useState<PropertyAnalysis | null>(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  // Fetch comprehensive property analysis
  const fetchAnalysis = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/property-analysis', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          address: property.address,
          propertyId: property.id
        }),
      });

      if (!response.ok) {
        throw new Error('Analysis failed');
      }

      const data = await response.json();
      setAnalysis(data.analysis);
    } catch (error) {
      console.error('Analysis error:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen && property) {
      fetchAnalysis();
    }
  }, [isOpen, property]);

  if (!isOpen) return null;

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: Eye },
    { id: 'financial', label: 'Financial', icon: DollarSign },
    { id: 'legal', label: 'Legal', icon: Shield },
    { id: 'development', label: 'Development', icon: Building },
    { id: 'risks', label: 'Risks', icon: AlertTriangle },
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        
        {/* Header */}
        <div className="bg-blue-600 text-white p-6 flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold">{property.address}</h2>
            <p className="text-blue-100">Comprehensive Land Analysis</p>
          </div>
          <button
            onClick={onClose}
            className="text-white hover:text-blue-200 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Tab Navigation */}
        <div className="border-b bg-gray-50">
          <div className="flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-2 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </div>
        </div>

        {/* Content */}
        <div className="p-6 max-h-[60vh] overflow-y-auto">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-600">Analyzing property...</span>
            </div>
          ) : (
            <>
              {/* Overview Tab */}
              {activeTab === 'overview' && (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center mb-2">
                        <DollarSign className="h-5 w-5 text-green-600 mr-2" />
                        <h3 className="font-semibold">Price</h3>
                      </div>
                      <p className="text-2xl font-bold text-green-600">{formatPrice(property.price)}</p>
                      {property.acres && (
                        <p className="text-sm text-gray-600">
                          ${Math.round(property.price / property.acres).toLocaleString()} per acre
                        </p>
                      )}
                    </div>
                    
                    {property.acres && (
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <div className="flex items-center mb-2">
                          <Ruler className="h-5 w-5 text-blue-600 mr-2" />
                          <h3 className="font-semibold">Size</h3>
                        </div>
                        <p className="text-2xl font-bold text-blue-600">{property.acres.toFixed(2)} acres</p>
                        <p className="text-sm text-gray-600">
                          {(property.acres * 43560).toLocaleString()} sq ft
                        </p>
                      </div>
                    )}
                    
                    {property.zoning && (
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <div className="flex items-center mb-2">
                          <Building className="h-5 w-5 text-purple-600 mr-2" />
                          <h3 className="font-semibold">Zoning</h3>
                        </div>
                        <p className="text-2xl font-bold text-purple-600">{property.zoning}</p>
                        <p className="text-sm text-gray-600">Current classification</p>
                      </div>
                    )}
                  </div>

                  {property.description && (
                    <div>
                      <h3 className="font-semibold mb-2">Property Description</h3>
                      <p className="text-gray-700 bg-gray-50 p-4 rounded-lg">{property.description}</p>
                    </div>
                  )}

                  <div>
                    <h3 className="font-semibold mb-2">Data Source</h3>
                    <span className={`inline-block px-3 py-1 rounded-full text-sm ${
                      property.source === 'RealEstateAPI' 
                        ? 'bg-blue-100 text-blue-800' 
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {property.source}
                    </span>
                  </div>
                </div>
              )}

              {/* Financial Tab */}
              {activeTab === 'financial' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold">Financial Analysis</h3>
                  
                  {analysis?.comparables && analysis.comparables.length > 0 ? (
                    <div>
                      <h4 className="font-medium mb-3">Comparable Properties</h4>
                      <div className="space-y-3">
                        {analysis.comparables.slice(0, 5).map((comp, index) => (
                          <div key={index} className="bg-gray-50 p-4 rounded-lg flex justify-between items-center">
                            <div>
                              <p className="font-medium">{comp.address || 'Address not available'}</p>
                              <p className="text-sm text-gray-600">
                                {comp.acres ? `${comp.acres} acres` : 'Size not available'}
                              </p>
                            </div>
                            <div className="text-right">
                              <p className="font-bold text-green-600">
                                {comp.price ? formatPrice(comp.price) : 'Price not available'}
                              </p>
                              {comp.pricePerAcre && (
                                <p className="text-sm text-gray-600">
                                  ${comp.pricePerAcre.toLocaleString()}/acre
                                </p>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
                      <p className="text-yellow-800">Comparable properties data is being analyzed...</p>
                    </div>
                  )}

                  {analysis?.investment && (
                    <div>
                      <h4 className="font-medium mb-3">Investment Potential</h4>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <p className="text-gray-700">Investment analysis available from PropBolt API integration.</p>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Legal Tab */}
              {activeTab === 'legal' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold">Legal Information</h3>
                  
                  <div>
                    <h4 className="font-medium mb-3 flex items-center">
                      <AlertTriangle className="h-4 w-4 text-red-500 mr-2" />
                      Liens and Encumbrances
                    </h4>
                    {analysis?.liens && analysis.liens.length > 0 ? (
                      <div className="space-y-3">
                        {analysis.liens.map((lien, index) => (
                          <div key={index} className="bg-red-50 border border-red-200 p-4 rounded-lg">
                            <p className="font-medium text-red-800">{lien.type || 'Lien Found'}</p>
                            <p className="text-red-700">{lien.description || 'Details not available'}</p>
                            {lien.amount && (
                              <p className="text-sm text-red-600">Amount: {formatPrice(lien.amount)}</p>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="bg-green-50 border border-green-200 p-4 rounded-lg">
                        <p className="text-green-800">No liens found in initial search.</p>
                      </div>
                    )}
                  </div>

                  <div>
                    <h4 className="font-medium mb-3 flex items-center">
                      <Users className="h-4 w-4 text-blue-500 mr-2" />
                      Owner Information
                    </h4>
                    {analysis?.ownerInfo ? (
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <p className="text-gray-700">Owner contact information available through SkipTrace API.</p>
                      </div>
                    ) : (
                      <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
                        <p className="text-yellow-800">Owner information is being researched...</p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Development Tab */}
              {activeTab === 'development' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold">Development Potential</h3>
                  
                  <div>
                    <h4 className="font-medium mb-3">Zoning Analysis</h4>
                    {analysis?.zoning ? (
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <p className="font-medium">Current Zoning</p>
                            <p className="text-gray-700">{analysis.zoning.current}</p>
                          </div>
                          <div>
                            <p className="font-medium">Development Potential</p>
                            <p className="text-gray-700">{analysis.zoning.developmentPotential}</p>
                          </div>
                        </div>
                        {analysis.zoning.permittedUses && analysis.zoning.permittedUses.length > 0 && (
                          <div className="mt-4">
                            <p className="font-medium mb-2">Permitted Uses</p>
                            <div className="flex flex-wrap gap-2">
                              {analysis.zoning.permittedUses.map((use: string, index: number) => (
                                <span key={index} className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">
                                  {use}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
                        <p className="text-yellow-800">Zoning analysis in progress...</p>
                      </div>
                    )}
                  </div>

                  <div>
                    <h4 className="font-medium mb-3 flex items-center">
                      <Zap className="h-4 w-4 text-yellow-500 mr-2" />
                      Utilities Assessment
                    </h4>
                    {analysis?.utilities ? (
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                          <div>
                            <p className="font-medium">Water</p>
                            <p className="text-gray-700">{analysis.utilities.water}</p>
                          </div>
                          <div>
                            <p className="font-medium">Sewer</p>
                            <p className="text-gray-700">{analysis.utilities.sewer}</p>
                          </div>
                          <div>
                            <p className="font-medium">Electric</p>
                            <p className="text-gray-700">{analysis.utilities.electric}</p>
                          </div>
                          <div>
                            <p className="font-medium">Gas</p>
                            <p className="text-gray-700">{analysis.utilities.gas}</p>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
                        <p className="text-yellow-800">Utilities assessment in progress...</p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Risks Tab */}
              {activeTab === 'risks' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold">Risk Assessment</h3>
                  
                  {analysis?.risks && analysis.risks.length > 0 ? (
                    <div className="space-y-4">
                      {analysis.risks.map((risk, index) => (
                        <div key={index} className={`p-4 rounded-lg border ${
                          risk.severity === 'High' ? 'bg-red-50 border-red-200' :
                          risk.severity === 'Medium' ? 'bg-yellow-50 border-yellow-200' :
                          'bg-blue-50 border-blue-200'
                        }`}>
                          <div className="flex items-center mb-2">
                            <AlertTriangle className={`h-4 w-4 mr-2 ${
                              risk.severity === 'High' ? 'text-red-500' :
                              risk.severity === 'Medium' ? 'text-yellow-500' :
                              'text-blue-500'
                            }`} />
                            <span className={`font-medium ${
                              risk.severity === 'High' ? 'text-red-800' :
                              risk.severity === 'Medium' ? 'text-yellow-800' :
                              'text-blue-800'
                            }`}>
                              {risk.type} Risk - {risk.severity}
                            </span>
                          </div>
                          <p className={`${
                            risk.severity === 'High' ? 'text-red-700' :
                            risk.severity === 'Medium' ? 'text-yellow-700' :
                            'text-blue-700'
                          }`}>
                            {risk.description}
                          </p>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="bg-green-50 border border-green-200 p-4 rounded-lg">
                      <p className="text-green-800">No significant risks identified in initial analysis.</p>
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 flex justify-between items-center border-t">
          <div className="text-sm text-gray-600">
            Analysis powered by RealEstateAPI.com + PropBolt dual integration
          </div>
          <div className="flex space-x-3">
            <button className="px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 flex items-center">
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </button>
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
