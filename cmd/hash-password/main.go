package main

import (
	"fmt"
	"log"
	"os"

	"golang.org/x/crypto/bcrypt"
)

func main() {
	if len(os.Args) != 2 {
		fmt.Println("Usage: go run hash-password.go <password>")
		fmt.Println("Example: go run hash-password.go mypassword123")
		os.Exit(1)
	}

	password := os.Args[1]
	
	// Generate hash
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		log.Fatalf("Failed to hash password: %v", err)
	}

	fmt.Printf("Password: %s\n", password)
	fmt.Printf("Hash: %s\n", string(hashedPassword))
	fmt.Println()
	fmt.Println("Use this hash in your SQL INSERT statement:")
	fmt.Printf("'%s'\n", string(hashedPassword))
}
