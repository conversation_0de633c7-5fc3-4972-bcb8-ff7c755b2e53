package main

import (
	"bufio"
	"fmt"
	"log"
	"os"
	"strings"

	"golang.org/x/crypto/bcrypt"
	"golang.org/x/term"
	"propbolt/database"
)

func main() {
	// Initialize database connection
	if err := database.InitDB(); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer database.CloseDB()

	fmt.Println("🚀 PropBolt User Creation Tool")
	fmt.Println("===============================")
	fmt.Println()

	reader := bufio.NewReader(os.Stdin)

	// Get user details
	fmt.Print("Enter username: ")
	username, _ := reader.ReadString('\n')
	username = strings.TrimSpace(username)

	fmt.Print("Enter email: ")
	email, _ := reader.ReadString('\n')
	email = strings.TrimSpace(email)

	// Get password securely
	fmt.Print("Enter password: ")
	passwordBytes, err := term.ReadPassword(int(os.Stdin.Fd()))
	if err != nil {
		log.Fatalf("Failed to read password: %v", err)
	}
	password := string(passwordBytes)
	fmt.Println() // New line after password input

	// Get role
	fmt.Print("Enter role (admin/user) [default: user]: ")
	role, _ := reader.ReadString('\n')
	role = strings.TrimSpace(role)
	if role == "" {
		role = "user"
	}

	// Get account type
	fmt.Println()
	fmt.Println("Account Types:")
	fmt.Println("  land - Access to land search dashboard")
	fmt.Println("  data - Access to API dashboard")
	fmt.Print("Enter account type (land/data): ")
	accountType, _ := reader.ReadString('\n')
	accountType = strings.TrimSpace(accountType)

	// Validate account type
	if accountType != "land" && accountType != "data" {
		log.Fatal("Invalid account type. Must be 'land' or 'data'")
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		log.Fatalf("Failed to hash password: %v", err)
	}

	// Create user
	err = database.CreateUser(username, email, string(hashedPassword), role, accountType)
	if err != nil {
		log.Fatalf("Failed to create user: %v", err)
	}

	fmt.Println()
	fmt.Println("✅ User created successfully!")
	fmt.Printf("   Username: %s\n", username)
	fmt.Printf("   Email: %s\n", email)
	fmt.Printf("   Role: %s\n", role)
	fmt.Printf("   Account Type: %s\n", accountType)
	fmt.Println()

	if accountType == "land" {
		fmt.Println("🔗 Login URL: https://propbolt.com/land")
	} else {
		fmt.Println("🔗 Login URL: https://propbolt.com/access")
	}
}
