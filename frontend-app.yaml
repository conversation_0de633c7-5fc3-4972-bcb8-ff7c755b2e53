runtime: nodejs20
service: frontend

# Environment variables for Next.js
env_variables:
  NODE_ENV: "production"
  NEXT_PUBLIC_API_BASE_URL: "https://propbolt.com"
  NEXT_PUBLIC_MAPBOX_TOKEN: "pk.eyJ1Ijoic2F2ZXVuZGVyY29udHJhY3QiLCJhIjoiY21icXYxbjZnMDN3czJrb2h5djd1bng0OSJ9.79dGToNOI5onemlC19dcDw"
  NEXT_PUBLIC_APP_NAME: "PropBolt Vacant Land Search"
  NEXT_PUBLIC_APP_DESCRIPTION: "Professional real estate admin panel for vacant land search in Daytona Beach, Florida"
  PORT: "8080"
  NEXTAUTH_URL: "https://propbolt.com"
  NEXTAUTH_SECRET: "propbolt-nextauth-secret-2024-production-key-v1"
  # Database connection for NextAuth
  DATABASE_URL: "**************************************************************************************************************************"
  # Google Cloud Platform
  GCP_PROJECT_ID: "gold-braid-458901-v2"
  GCP_PROJECT_NUMBER: "456078002475"

# Automatic scaling configuration
automatic_scaling:
  min_instances: 1
  max_instances: 5
  target_cpu_utilization: 0.6

# Resource allocation
resources:
  cpu: 1
  memory_gb: 1
