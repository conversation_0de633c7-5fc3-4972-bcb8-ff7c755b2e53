-- Prop<PERSON>olt User Creation SQL Script
-- Run this script to create users directly in PostgreSQL

-- Template for creating users in production
-- Replace placeholder values with actual user data
-- Use the create-user.sh script or hash-password.go to generate secure password hashes

-- Template: Create a land search admin user
-- INSERT INTO users (username, email, password_hash, role, account_type, created_at, updated_at)
-- VALUES (
--     'REPLACE_WITH_ACTUAL_NAME',
--     'REPLACE_WITH_ACTUAL_EMAIL',
--     'REPLACE_WITH_ACTUAL_HASH', -- Use hash-password.go to generate
--     'admin',
--     'land',
--     NOW(),
--     NOW()
-- );

-- Template: Create a data API user
-- INSERT INTO users (username, email, password_hash, role, account_type, created_at, updated_at)
-- VALUES (
--     'REPLACE_WITH_ACTUAL_NAME',
--     'REPLACE_WITH_ACTUAL_EMAIL',
--     'REPLACE_WITH_ACTUAL_HASH', -- Use hash-password.go to generate
--     'user',
--     'data',
--     NOW(),
--     NOW()
-- );

-- To generate a new password hash, use this in Go:
-- hashedPassword, _ := bcrypt.GenerateFromPassword([]byte("your-password"), bcrypt.DefaultCost)
-- fmt.Println(string(hashedPassword))

-- Account Types:
-- 'land' - Access to land search dashboard at propbolt.com/land
-- 'data' - Access to API dashboard at propbolt.com/access
-- NULL   - No access (login denied)

-- Check existing users:
-- SELECT id, username, email, role, account_type, created_at FROM users;
