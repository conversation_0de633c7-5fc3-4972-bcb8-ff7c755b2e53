package middleware

import (
    "context"
    "database/sql"
    "net/http"
    "strings"
    "time"

    "github.com/brycebayens/v1-go/models"
)

// APIKeyAuthMiddleware validates API keys for protected endpoints
func APIKeyAuthMiddleware(db *sql.DB) func(http.Handler) http.Handler {
    return func(next http.Handler) http.Handler {
        return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
            // Extract API key from Authorization header
            authHeader := r.Header.Get("Authorization")
            if authHeader == "" {
                http.Error(w, "API key required", http.StatusUnauthorized)
                return
            }

            // Check if it's in the format "Bearer API_KEY"
            parts := strings.Split(authHeader, " ")
            var apiKey string
            if len(parts) == 2 && strings.ToLower(parts[0]) == "bearer" {
                apiKey = parts[1]
            } else {
                apiKey = authHeader // Allow raw API key for simplicity
            }

            // Validate API key
            apiKeyID, userID, err := models.ValidateAPIKey(db, apiKey)
            if err != nil {
                http.Error(w, "Invalid API key: "+err.Error(), http.StatusUnauthorized)
                return
            }

            // Store API key ID and user ID in context
            ctx := context.WithValue(r.Context(), "apiKeyID", apiKeyID)
            ctx = context.WithValue(ctx, "userID", userID)

            // Track API usage
            endpoint := r.URL.Path
            go func() {
                // Track usage asynchronously to not block the request
                err := models.TrackAPIUsage(db, apiKeyID, endpoint)
                if err != nil {
                    // Just log the error, don't fail the request
                    // In production, you'd use a proper logger
                    println("Error tracking API usage:", err.Error())
                }
            }()

            // Continue with the request
            next.ServeHTTP(w, r.WithContext(ctx))
        })
    }
}

// RateLimitMiddleware implements rate limiting for API endpoints
func RateLimitMiddleware(db *sql.DB) func(http.Handler) http.Handler {
    // In-memory cache to reduce database load
    // In production, you might use Redis for distributed rate limiting
    type cacheEntry struct {
        count     int
        timestamp time.Time
    }
    cache := make(map[int]cacheEntry)

    return func(next http.Handler) http.Handler {
        return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
            // Get API key ID from context (set by APIKeyAuthMiddleware)
            apiKeyID, ok := r.Context().Value("apiKeyID").(int)
            if !ok {
                // If no API key ID in context, skip rate limiting
                next.ServeHTTP(w, r)
                return
            }

            // Check if we have a cached entry
            now := time.Now()
            entry, exists := cache[apiKeyID]
            
            // If entry exists and is from today
            if exists && entry.timestamp.Day() == now.Day() && 
               entry.timestamp.Month() == now.Month() && 
               entry.timestamp.Year() == now.Year() {
                
                // Get rate limit for this API key
                var rateLimit int
                err := db.QueryRow("SELECT rate_limit FROM api_keys WHERE id = $1", apiKeyID).Scan(&rateLimit)
                if err != nil {
                    http.Error(w, "Error retrieving rate limit", http.StatusInternalServerError)
                    return
                }
                
                // Check if rate limit exceeded
                if entry.count >= rateLimit {
                    w.Header().Set("Content-Type", "application/json")
                    w.Header().Set("X-RateLimit-Limit", string(rateLimit))
                    w.Header().Set("X-RateLimit-Remaining", "0")
                    w.WriteHeader(http.StatusTooManyRequests)
                    w.Write([]byte(`{"error": "Rate limit exceeded", "message": "You have exceeded your daily API request quota"}`))
                    return
                }
                
                // Update cache
                cache[apiKeyID] = cacheEntry{
                    count:     entry.count + 1,
                    timestamp: now,
                }
            } else {
                // Get today's usage from database
                var todayUsage int
                err := db.QueryRow(`
                    SELECT COALESCE(SUM(request_count), 0)
                    FROM api_usage
                    WHERE api_key_id = $1 AND date = CURRENT_DATE
                `, apiKeyID).Scan(&todayUsage)
                
                if err != nil {
                    http.Error(w, "Error checking rate limit", http.StatusInternalServerError)
                    return
                }
                
                // Get rate limit for this API key
                var rateLimit int
                err = db.QueryRow("SELECT rate_limit FROM api_keys WHERE id = $1", apiKeyID).Scan(&rateLimit)
                if err != nil {
                    http.Error(w, "Error retrieving rate limit", http.StatusInternalServerError)
                    return
                }
                
                // Check if rate limit exceeded
                if todayUsage >= rateLimit {
                    w.Header().Set("Content-Type", "application/json")
                    w.Header().Set("X-RateLimit-Limit", string(rateLimit))
                    w.Header().Set("X-RateLimit-Remaining", "0")
                    w.WriteHeader(http.StatusTooManyRequests)
                    w.Write([]byte(`{"error": "Rate limit exceeded", "message": "You have exceeded your daily API request quota"}`))
                    return
                }
                
                // Update cache
                cache[apiKeyID] = cacheEntry{
                    count:     todayUsage + 1,
                    timestamp: now,
                }
            }
            
            // Continue with the request
            next.ServeHTTP(w, r)
        })
    }
}