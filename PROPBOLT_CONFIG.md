# PropBolt Configuration & Architecture

## Project Separation

This PropBolt project is **entirely separate** from any other projects and uses its own dedicated Google Cloud infrastructure.

### Google Cloud Project Details
- **Project ID**: `gold-braid-458901-v2`
- **Project Number**: `456078002475`
- **Project Name**: "My First Project"

## Domain Architecture

### Production Domains
- **propbolt.com** - Main domain for both frontend (Next.js) and backend API (Go)
- **www.propbolt.com** - Redirect to propbolt.com

### Authentication Flow
```
propbolt.com/login
    ↓
    Determines user type
    ↓
┌─────────────────┬─────────────────┐
│   Admin User    │   Regular User  │
│       ↓         │       ↓         │
│ admin.propbolt  │ go.propbolt.com │
│     .com        │                 │
└─────────────────┴─────────────────┘
```

## Service Architecture

### App Engine Services
1. **default** service (Go backend)
   - Handles: brain.propbolt.com, api.propbolt.com, admin.propbolt.com, go.propbolt.com
   - Runtime: go121
   - Auto-scaling enabled

2. **frontend** service (Next.js)
   - Handles: propbolt.com, www.propbolt.com
   - Runtime: nodejs20
   - Auto-scaling enabled

### API Endpoints

#### RealEstateAPI Proxy Endpoints (Admin Only - propbolt.com/api)
- `/api/v1/proxy/autocomplete` - Location autocomplete
- `/api/v1/proxy/property-search` - Property search
- `/api/v1/proxy/property-detail` - Single property details
- `/api/v1/proxy/property-detail-bulk` - Bulk property details
- `/api/v1/proxy/mapping` - Property mapping
- `/api/v1/proxy/property-comps-v3` - Property comparables v3
- `/api/v1/proxy/property-comps-v2` - Property comparables v2
- `/api/v1/proxy/involuntary-liens` - Involuntary liens data
- `/api/v1/proxy/skiptrace` - Skip tracing

#### User API Endpoints (propbolt.com/api)
- `/api/v1/properties/search` - User property search
- `/api/v1/properties/details` - User property details
- `/health` - Health check

#### Legacy Brain Endpoints (Backward Compatibility)
- `/api/v1/brain/*` - Legacy admin endpoints

## Database Configuration

### Cloud SQL PostgreSQL
- **Instance**: Production PostgreSQL instance
- **SSL/TLS**: Enabled
- **Firewall**: Restricted to App Engine
- **Backups**: Automated daily backups
- **High Availability**: Enabled for production

### Database Schema
- **users** table with account_type column ('land', 'data')
- **properties** table for cached property data
- **analytics** table for usage tracking

## Security Configuration

### SSL/TLS
- All domains have SSL certificates managed by Google
- HTTPS enforced for all traffic
- HSTS headers enabled

### CORS Configuration
- Allowed origins include all PropBolt domains
- Credentials allowed for authenticated requests
- Preflight requests handled properly

### Authentication
- BetterAuth integration planned
- Session-based authentication
- Account type-based access control

## Deployment Configuration

### Environment Variables
- `REALESTATE_API_KEY` - RealEstateAPI.com API key
- `DATABASE_URL` - Cloud SQL connection string
- `PROXY_ENDPOINTS` - Smartproxy configuration
- `ENVIRONMENT` - production/development

### Proxy Configuration (Smartproxy)
- **Endpoints**: gate.decodo.com:10001-10010
- **Username**: sp0o8xf1er
- **Password**: 4Qw0OuwKmQ5=tluzj1
- **Format**: host:port:username:password

## Monitoring & Logging

### Google Cloud Monitoring
- Application performance monitoring
- Error tracking and alerting
- Resource utilization monitoring
- Custom metrics for API usage

### Logging
- Structured logging with Cloud Logging
- Request/response logging
- Error logging with stack traces
- Performance metrics logging

## Backup & Recovery

### Automated Backups
- Database: Daily automated backups with 30-day retention
- Application: Source code in Git repository
- Configuration: Infrastructure as Code

### Disaster Recovery
- Multi-zone deployment for high availability
- Automated failover for database
- Application auto-scaling and health checks

## Development Workflow

### Local Development
- Go backend: `go run main.go`
- Next.js frontend: `npm run dev`
- Local PostgreSQL for development

### Deployment
- Use `./deploy-clean.sh` for clean deployment
- Automated CI/CD pipeline (planned)
- Blue-green deployment strategy

## Cost Optimization

### App Engine
- Auto-scaling based on traffic
- Minimum instances: 0 (scales to zero)
- Maximum instances: 10 per service

### Cloud SQL
- Shared-core instance for development
- Dedicated cores for production
- Automated scaling based on usage

## Compliance & Privacy

### Data Protection
- GDPR compliance for EU users
- Data encryption at rest and in transit
- Regular security audits

### API Rate Limiting
- Per-user rate limiting
- API key-based throttling
- DDoS protection via Google Cloud Armor
