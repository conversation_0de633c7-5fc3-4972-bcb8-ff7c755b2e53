package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"
	"propbolt/realestate"
	"propbolt/database"
)

// VacantLandSearchRequest represents a comprehensive vacant land search request
type VacantLandSearchRequest struct {
	// Location Parameters (flexible input)
	Query       string  `json:"query"`        // "Daytona Beach, FL" or "32114" or "123 Main St"
	Address     string  `json:"address"`      // Full address
	City        string  `json:"city"`         // City name
	State       string  `json:"state"`        // State abbreviation
	ZipCode     string  `json:"zipCode"`      // ZIP code
	County      string  `json:"county"`       // County name
	Latitude    float64 `json:"latitude"`     // Latitude coordinate
	Longitude   float64 `json:"longitude"`    // Longitude coordinate
	Radius      float64 `json:"radius"`       // Search radius in miles
	
	// Geographic Bounds (for map-based searches)
	NorthEast   *Coordinate `json:"northEast,omitempty"`
	SouthWest   *Coordinate `json:"southWest,omitempty"`
	
	// Property Filters (Vacant Land Specific)
	PropertyTypes    []string `json:"propertyTypes"`    // ["land", "vacant_land", "lot"]
	LandUseTypes     []string `json:"landUseTypes"`     // ["residential", "commercial", "agricultural"]
	ZoningTypes      []string `json:"zoningTypes"`      // ["R1", "R2", "C1", "A1"]
	
	// Size Filters
	MinLotSizeSqft   int     `json:"minLotSizeSqft"`   // Minimum lot size in square feet
	MaxLotSizeSqft   int     `json:"maxLotSizeSqft"`   // Maximum lot size in square feet
	MinAcres         float64 `json:"minAcres"`         // Minimum acreage
	MaxAcres         float64 `json:"maxAcres"`         // Maximum acreage
	
	// Price Filters
	MinPrice         int     `json:"minPrice"`         // Minimum price
	MaxPrice         int     `json:"maxPrice"`         // Maximum price
	MaxPricePerSqft  float64 `json:"maxPricePerSqft"`  // Maximum price per square foot
	
	// Utilities & Infrastructure
	RequireUtilities bool    `json:"requireUtilities"` // Must have utilities
	RequireWater     bool    `json:"requireWater"`     // Must have water access
	RequireSewer     bool    `json:"requireSewer"`     // Must have sewer access
	RequireElectric  bool    `json:"requireElectric"`  // Must have electric access
	RequireGas       bool    `json:"requireGas"`       // Must have gas access
	RequireRoadAccess bool   `json:"requireRoadAccess"` // Must have road access
	
	// Environmental & Location Features
	WaterfrontOnly   bool    `json:"waterfrontOnly"`   // Waterfront properties only
	ViewRequired     bool    `json:"viewRequired"`     // Must have view
	BuildableOnly    bool    `json:"buildableOnly"`    // Buildable lots only
	FloodZoneExclude []string `json:"floodZoneExclude"` // Exclude flood zones (e.g., ["A", "AE"])
	
	// Market Filters
	MaxDaysOnMarket  int      `json:"maxDaysOnMarket"`  // Maximum days on market
	ListingStatuses  []string `json:"listingStatuses"`  // ["active", "pending", "contingent"]
	MLSActiveOnly    bool     `json:"mlsActiveOnly"`    // MLS active listings only
	
	// Search Parameters
	Limit            int      `json:"limit"`            // Number of results (default: 25)
	Offset           int      `json:"offset"`           // Pagination offset
	SortBy           string   `json:"sortBy"`           // "price", "size", "date", "pricePerSqft"
	SortOrder        string   `json:"sortOrder"`        // "asc", "desc"
	
	// API Selection
	UseRealEstateAPI bool     `json:"useRealEstateAPI"` // Use RealEstateAPI.com
	UsePropBoltAPI   bool     `json:"usePropBoltAPI"`   // Use internal PropBolt API
	CombineResults   bool     `json:"combineResults"`   // Combine results from both APIs
}

// Coordinate represents a geographic coordinate
type Coordinate struct {
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
}

// VacantLandProperty represents a vacant land property result
type VacantLandProperty struct {
	ID               string                 `json:"id"`
	Source           string                 `json:"source"`           // "RealEstateAPI" or "PropBolt"
	Address          string                 `json:"address"`
	City             string                 `json:"city"`
	State            string                 `json:"state"`
	ZipCode          string                 `json:"zipCode"`
	County           string                 `json:"county"`
	
	// Location
	Latitude         float64                `json:"latitude"`
	Longitude        float64                `json:"longitude"`
	
	// Property Details
	Price            float64                `json:"price"`
	PricePerSqft     float64                `json:"pricePerSqft"`
	LotSizeSqft      int                    `json:"lotSizeSqft"`
	Acres            float64                `json:"acres"`
	PropertyType     string                 `json:"propertyType"`
	LandUse          string                 `json:"landUse"`
	Zoning           string                 `json:"zoning"`
	
	// Utilities & Infrastructure
	HasUtilities     bool                   `json:"hasUtilities"`
	HasWater         bool                   `json:"hasWater"`
	HasSewer         bool                   `json:"hasSewer"`
	HasElectric      bool                   `json:"hasElectric"`
	HasGas           bool                   `json:"hasGas"`
	HasRoadAccess    bool                   `json:"hasRoadAccess"`
	
	// Environmental Features
	IsWaterfront     bool                   `json:"isWaterfront"`
	HasView          bool                   `json:"hasView"`
	IsBuildable      bool                   `json:"isBuildable"`
	FloodZone        string                 `json:"floodZone"`
	
	// Market Information
	DaysOnMarket     int                    `json:"daysOnMarket"`
	ListingStatus    string                 `json:"listingStatus"`
	MLSNumber        string                 `json:"mlsNumber"`
	ListingDate      string                 `json:"listingDate"`
	
	// Additional Data
	Description      string                 `json:"description"`
	Photos           []string               `json:"photos"`
	VirtualTour      string                 `json:"virtualTour"`
	
	// Owner Information (if available)
	OwnerName        string                 `json:"ownerName,omitempty"`
	OwnerAddress     string                 `json:"ownerAddress,omitempty"`
	
	// Tax Information
	TaxAssessedValue float64                `json:"taxAssessedValue"`
	AnnualTaxes      float64                `json:"annualTaxes"`
	
	// Raw data from source API
	RawData          map[string]interface{} `json:"rawData,omitempty"`
}

// VacantLandSearchResponse represents the search response
type VacantLandSearchResponse struct {
	Properties       []VacantLandProperty   `json:"properties"`
	Total            int                    `json:"total"`
	Offset           int                    `json:"offset"`
	Limit            int                    `json:"limit"`
	Sources          map[string]int         `json:"sources"`          // Count by source
	SearchParams     VacantLandSearchRequest `json:"searchParams"`
	ExecutionTime    string                 `json:"executionTime"`
	Errors           []string               `json:"errors,omitempty"`
}

// VacantLandSearchEngine handles comprehensive vacant land searches
type VacantLandSearchEngine struct {
	realEstateClient *realestate.Client
	dbClient         *database.Database
}

// NewVacantLandSearchEngine creates a new search engine
func NewVacantLandSearchEngine() *VacantLandSearchEngine {
	return &VacantLandSearchEngine{
		realEstateClient: realestate.NewClient(),
		dbClient:         database.GetDB(),
	}
}

// parseLocationQuery parses flexible location input
func (engine *VacantLandSearchEngine) parseLocationQuery(query string) map[string]interface{} {
	query = strings.TrimSpace(query)
	if query == "" {
		return nil
	}
	
	// Check if it's a ZIP code (5 digits)
	if len(query) == 5 {
		if _, err := strconv.Atoi(query); err == nil {
			return map[string]interface{}{
				"zipCode": query,
			}
		}
	}
	
	// Check if it's a full address (contains numbers and street indicators)
	if strings.ContainsAny(query, "0123456789") && 
	   (strings.Contains(strings.ToLower(query), "st") || 
	    strings.Contains(strings.ToLower(query), "ave") || 
	    strings.Contains(strings.ToLower(query), "rd") || 
	    strings.Contains(strings.ToLower(query), "dr") || 
	    strings.Contains(strings.ToLower(query), "blvd")) {
		return map[string]interface{}{
			"address": query,
		}
	}
	
	// Parse city, state format
	parts := strings.Split(query, ",")
	if len(parts) == 2 {
		city := strings.TrimSpace(parts[0])
		state := strings.TrimSpace(parts[1])
		return map[string]interface{}{
			"city":  city,
			"state": state,
		}
	}
	
	// Default to city search
	return map[string]interface{}{
		"city": query,
	}
}

// buildRealEstateAPIQuery builds query for RealEstateAPI.com
func (engine *VacantLandSearchEngine) buildRealEstateAPIQuery(req VacantLandSearchRequest) map[string]interface{} {
	query := make(map[string]interface{})
	
	// Location parameters
	if req.Query != "" {
		locationParams := engine.parseLocationQuery(req.Query)
		for k, v := range locationParams {
			query[k] = v
		}
	}
	
	// Override with specific location fields if provided
	if req.Address != "" {
		query["address"] = req.Address
	}
	if req.City != "" {
		query["city"] = req.City
	}
	if req.State != "" {
		query["state"] = req.State
	}
	if req.ZipCode != "" {
		query["zipCode"] = req.ZipCode
	}
	if req.County != "" {
		query["county"] = req.County
	}
	if req.Latitude != 0 && req.Longitude != 0 {
		query["latitude"] = req.Latitude
		query["longitude"] = req.Longitude
		if req.Radius > 0 {
			query["radius"] = req.Radius
		} else {
			query["radius"] = 10 // Default 10 miles
		}
	}
	
	// Geographic bounds
	if req.NorthEast != nil && req.SouthWest != nil {
		query["bounds"] = map[string]interface{}{
			"north": req.NorthEast.Latitude,
			"east":  req.NorthEast.Longitude,
			"south": req.SouthWest.Latitude,
			"west":  req.SouthWest.Longitude,
		}
	}
	
	// Property type filters - focus on vacant land
	propertyTypes := req.PropertyTypes
	if len(propertyTypes) == 0 {
		propertyTypes = []string{"land", "vacant_land", "lot"}
	}
	query["propertyType"] = propertyTypes
	
	// Land use types
	if len(req.LandUseTypes) > 0 {
		query["landUse"] = req.LandUseTypes
	}
	
	// Zoning types
	if len(req.ZoningTypes) > 0 {
		query["zoning"] = req.ZoningTypes
	}
	
	// Size filters
	if req.MinLotSizeSqft > 0 {
		query["minLotSize"] = req.MinLotSizeSqft
	}
	if req.MaxLotSizeSqft > 0 {
		query["maxLotSize"] = req.MaxLotSizeSqft
	}
	if req.MinAcres > 0 {
		query["minAcres"] = req.MinAcres
	}
	if req.MaxAcres > 0 {
		query["maxAcres"] = req.MaxAcres
	}
	
	// Price filters
	if req.MinPrice > 0 {
		query["minPrice"] = req.MinPrice
	}
	if req.MaxPrice > 0 {
		query["maxPrice"] = req.MaxPrice
	}
	if req.MaxPricePerSqft > 0 {
		query["maxPricePerSqft"] = req.MaxPricePerSqft
	}
	
	// Utilities and infrastructure
	if req.RequireUtilities {
		query["hasUtilities"] = true
	}
	if req.RequireWater {
		query["hasWater"] = true
	}
	if req.RequireSewer {
		query["hasSewer"] = true
	}
	if req.RequireElectric {
		query["hasElectric"] = true
	}
	if req.RequireGas {
		query["hasGas"] = true
	}
	if req.RequireRoadAccess {
		query["hasRoadAccess"] = true
	}
	
	// Environmental features
	if req.WaterfrontOnly {
		query["isWaterfront"] = true
	}
	if req.ViewRequired {
		query["hasView"] = true
	}
	if req.BuildableOnly {
		query["isBuildable"] = true
	}
	if len(req.FloodZoneExclude) > 0 {
		query["excludeFloodZones"] = req.FloodZoneExclude
	}
	
	// Market filters
	if req.MaxDaysOnMarket > 0 {
		query["maxDaysOnMarket"] = req.MaxDaysOnMarket
	}
	if len(req.ListingStatuses) > 0 {
		query["listingStatus"] = req.ListingStatuses
	} else {
		query["listingStatus"] = []string{"active"} // Default to active listings
	}
	if req.MLSActiveOnly {
		query["mlsActive"] = true
	}
	
	// Sorting
	if req.SortBy != "" {
		query["sortBy"] = req.SortBy
	}
	if req.SortOrder != "" {
		query["sortOrder"] = req.SortOrder
	}
	
	return query
}

// searchRealEstateAPI searches using RealEstateAPI.com
func (engine *VacantLandSearchEngine) searchRealEstateAPI(req VacantLandSearchRequest) ([]VacantLandProperty, error) {
	query := engine.buildRealEstateAPIQuery(req)
	
	limit := req.Limit
	if limit <= 0 || limit > 1000 {
		limit = 25
	}
	
	offset := req.Offset
	if offset < 0 {
		offset = 0
	}
	
	log.Printf("Searching RealEstateAPI with query: %+v", query)
	
	response, err := engine.realEstateClient.PropertySearch(query, limit, offset, false)
	if err != nil {
		return nil, fmt.Errorf("RealEstateAPI search failed: %v", err)
	}
	
	properties := make([]VacantLandProperty, 0, len(response.Properties))
	
	for _, prop := range response.Properties {
		property := VacantLandProperty{
			ID:            fmt.Sprintf("real-estate-%s", prop.ID),
			Source:        "RealEstateAPI",
			Address:       prop.Address,
			City:          prop.City,
			State:         prop.State,
			ZipCode:       prop.ZipCode,
			Latitude:      prop.Latitude,
			Longitude:     prop.Longitude,
			Price:         prop.Price,
			RawData:       prop.PropertyInfo,
		}
		
		// Extract additional information from PropertyInfo
		if prop.PropertyInfo != nil {
			if lotSize, ok := prop.PropertyInfo["lotSize"].(float64); ok {
				property.LotSizeSqft = int(lotSize)
				property.Acres = lotSize / 43560 // Convert sq ft to acres
			}
			
			if pricePerSqft, ok := prop.PropertyInfo["pricePerSqft"].(float64); ok {
				property.PricePerSqft = pricePerSqft
			}
			
			if zoning, ok := prop.PropertyInfo["zoning"].(string); ok {
				property.Zoning = zoning
			}
			
			if propertyType, ok := prop.PropertyInfo["propertyType"].(string); ok {
				property.PropertyType = propertyType
			}
			
			if description, ok := prop.PropertyInfo["description"].(string); ok {
				property.Description = description
			}
			
			// Utilities
			if hasUtilities, ok := prop.PropertyInfo["hasUtilities"].(bool); ok {
				property.HasUtilities = hasUtilities
			}
			if hasWater, ok := prop.PropertyInfo["hasWater"].(bool); ok {
				property.HasWater = hasWater
			}
			if hasSewer, ok := prop.PropertyInfo["hasSewer"].(bool); ok {
				property.HasSewer = hasSewer
			}
			if hasElectric, ok := prop.PropertyInfo["hasElectric"].(bool); ok {
				property.HasElectric = hasElectric
			}
			
			// Environmental features
			if isWaterfront, ok := prop.PropertyInfo["isWaterfront"].(bool); ok {
				property.IsWaterfront = isWaterfront
			}
			if isBuildable, ok := prop.PropertyInfo["isBuildable"].(bool); ok {
				property.IsBuildable = isBuildable
			}
			if floodZone, ok := prop.PropertyInfo["floodZone"].(string); ok {
				property.FloodZone = floodZone
			}
			
			// Market information
			if daysOnMarket, ok := prop.PropertyInfo["daysOnMarket"].(float64); ok {
				property.DaysOnMarket = int(daysOnMarket)
			}
			if listingStatus, ok := prop.PropertyInfo["listingStatus"].(string); ok {
				property.ListingStatus = listingStatus
			}
		}
		
		properties = append(properties, property)
	}
	
	log.Printf("RealEstateAPI returned %d properties", len(properties))
	return properties, nil
}

// searchPropBoltAPI searches using internal PropBolt API
func (engine *VacantLandSearchEngine) searchPropBoltAPI(req VacantLandSearchRequest) ([]VacantLandProperty, error) {
	// Build query for internal API
	query := map[string]interface{}{
		"isLotLand":  true,
		"isAllHomes": false,
	}

	// Location parsing
	if req.Query != "" {
		locationParams := engine.parseLocationQuery(req.Query)
		for k, v := range locationParams {
			query[k] = v
		}
	}

	// Price filters
	if req.MinPrice > 0 {
		query["priceMin"] = req.MinPrice
	}
	if req.MaxPrice > 0 {
		query["priceMax"] = req.MaxPrice
	}

	// Size filters (convert acres to square feet for internal API)
	if req.MinAcres > 0 {
		query["minLotSize"] = int(req.MinAcres * 43560)
	}
	if req.MaxAcres > 0 {
		query["maxLotSize"] = int(req.MaxAcres * 43560)
	}

	log.Printf("Searching PropBolt API with query: %+v", query)

	// Query internal database
	properties, err := engine.queryInternalDatabase(query, req.Limit, req.Offset)
	if err != nil {
		return nil, fmt.Errorf("PropBolt API search failed: %v", err)
	}

	log.Printf("PropBolt API returned %d properties", len(properties))
	return properties, nil
}

// queryInternalDatabase queries the internal PropBolt database
func (engine *VacantLandSearchEngine) queryInternalDatabase(filters map[string]interface{}, limit, offset int) ([]VacantLandProperty, error) {
	// Build SQL query
	baseQuery := `
		SELECT id, address, price, size, zoning, latitude, longitude,
		       description, habitability, proximity, chain_lease_potential,
		       days_on_market, price_per_sqft, created_at
		FROM properties
		WHERE 1=1
	`

	args := []interface{}{}
	argIndex := 1

	// Add filters
	if minPrice, ok := filters["priceMin"].(int); ok && minPrice > 0 {
		baseQuery += fmt.Sprintf(" AND price >= $%d", argIndex)
		args = append(args, minPrice)
		argIndex++
	}

	if maxPrice, ok := filters["priceMax"].(int); ok && maxPrice > 0 {
		baseQuery += fmt.Sprintf(" AND price <= $%d", argIndex)
		args = append(args, maxPrice)
		argIndex++
	}

	if city, ok := filters["city"].(string); ok && city != "" {
		baseQuery += fmt.Sprintf(" AND address ILIKE $%d", argIndex)
		args = append(args, "%"+city+"%")
		argIndex++
	}

	if zipCode, ok := filters["zipCode"].(string); ok && zipCode != "" {
		baseQuery += fmt.Sprintf(" AND address ILIKE $%d", argIndex)
		args = append(args, "%"+zipCode+"%")
		argIndex++
	}

	// Add ordering
	baseQuery += " ORDER BY created_at DESC"

	// Add pagination
	if limit <= 0 || limit > 1000 {
		limit = 25
	}
	if offset < 0 {
		offset = 0
	}

	baseQuery += fmt.Sprintf(" LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
	args = append(args, limit, offset)

	log.Printf("Executing query: %s with args: %+v", baseQuery, args)

	rows, err := engine.dbClient.DB.Query(baseQuery, args...)
	if err != nil {
		return nil, fmt.Errorf("database query failed: %v", err)
	}
	defer rows.Close()

	properties := make([]VacantLandProperty, 0)

	for rows.Next() {
		var prop VacantLandProperty
		var size, description, habitability, proximity, chainLease *string
		var pricePerSqft *float64
		var createdAt string

		err := rows.Scan(
			&prop.ID,
			&prop.Address,
			&prop.Price,
			&size,
			&prop.Zoning,
			&prop.Latitude,
			&prop.Longitude,
			&description,
			&habitability,
			&proximity,
			&chainLease,
			&prop.DaysOnMarket,
			&pricePerSqft,
			&createdAt,
		)
		if err != nil {
			log.Printf("Error scanning row: %v", err)
			continue
		}

		// Set source and process optional fields
		prop.Source = "PropBolt"
		prop.ID = fmt.Sprintf("propbolt-%s", prop.ID)

		if size != nil && *size != "" {
			// Parse size (could be in acres or sq ft)
			if strings.Contains(strings.ToLower(*size), "acre") {
				// Extract numeric value and convert to sq ft
				var acres float64
				fmt.Sscanf(*size, "%f", &acres)
				prop.Acres = acres
				prop.LotSizeSqft = int(acres * 43560)
			} else {
				// Assume square feet
				var sqft int
				fmt.Sscanf(*size, "%d", &sqft)
				prop.LotSizeSqft = sqft
				prop.Acres = float64(sqft) / 43560
			}
		}

		if description != nil {
			prop.Description = *description
		}

		if pricePerSqft != nil {
			prop.PricePerSqft = *pricePerSqft
		} else if prop.LotSizeSqft > 0 {
			prop.PricePerSqft = prop.Price / float64(prop.LotSizeSqft)
		}

		// Set default values for vacant land
		prop.PropertyType = "land"
		prop.ListingStatus = "active"

		properties = append(properties, prop)
	}

	return properties, nil
}

// Search performs a comprehensive vacant land search
func (engine *VacantLandSearchEngine) Search(req VacantLandSearchRequest) (*VacantLandSearchResponse, error) {
	startTime := time.Now()

	response := &VacantLandSearchResponse{
		Properties:   make([]VacantLandProperty, 0),
		SearchParams: req,
		Sources:      make(map[string]int),
		Errors:       make([]string, 0),
	}

	// Set defaults
	if req.Limit <= 0 {
		req.Limit = 25
	}
	if req.Offset < 0 {
		req.Offset = 0
	}

	// Default to using both APIs if not specified
	if !req.UseRealEstateAPI && !req.UsePropBoltAPI {
		req.UseRealEstateAPI = true
		req.UsePropBoltAPI = true
		req.CombineResults = true
	}

	// Search RealEstateAPI
	if req.UseRealEstateAPI {
		properties, err := engine.searchRealEstateAPI(req)
		if err != nil {
			log.Printf("RealEstateAPI search error: %v", err)
			response.Errors = append(response.Errors, fmt.Sprintf("RealEstateAPI: %v", err))
		} else {
			response.Properties = append(response.Properties, properties...)
			response.Sources["RealEstateAPI"] = len(properties)
		}
	}

	// Search PropBolt API
	if req.UsePropBoltAPI {
		properties, err := engine.searchPropBoltAPI(req)
		if err != nil {
			log.Printf("PropBolt API search error: %v", err)
			response.Errors = append(response.Errors, fmt.Sprintf("PropBolt: %v", err))
		} else {
			if req.CombineResults {
				response.Properties = append(response.Properties, properties...)
			} else {
				response.Properties = properties
			}
			response.Sources["PropBolt"] = len(properties)
		}
	}

	// Remove duplicates if combining results
	if req.CombineResults && len(response.Properties) > 0 {
		response.Properties = engine.removeDuplicates(response.Properties)
	}

	// Sort results
	if req.SortBy != "" {
		engine.sortProperties(response.Properties, req.SortBy, req.SortOrder)
	}

	// Apply pagination to combined results
	total := len(response.Properties)
	if req.Offset < total {
		end := req.Offset + req.Limit
		if end > total {
			end = total
		}
		response.Properties = response.Properties[req.Offset:end]
	} else {
		response.Properties = []VacantLandProperty{}
	}

	response.Total = total
	response.Offset = req.Offset
	response.Limit = req.Limit
	response.ExecutionTime = time.Since(startTime).String()

	log.Printf("Search completed in %s, found %d properties", response.ExecutionTime, response.Total)

	return response, nil
}

// removeDuplicates removes duplicate properties based on address similarity
func (engine *VacantLandSearchEngine) removeDuplicates(properties []VacantLandProperty) []VacantLandProperty {
	seen := make(map[string]bool)
	unique := make([]VacantLandProperty, 0)

	for _, prop := range properties {
		// Create a normalized address key
		key := strings.ToLower(strings.ReplaceAll(prop.Address, " ", ""))
		if !seen[key] {
			seen[key] = true
			unique = append(unique, prop)
		}
	}

	log.Printf("Removed %d duplicates, %d unique properties remain", len(properties)-len(unique), len(unique))
	return unique
}

// sortProperties sorts properties by the specified field and order
func (engine *VacantLandSearchEngine) sortProperties(properties []VacantLandProperty, sortBy, sortOrder string) {
	// Implementation would depend on the sorting requirements
	// For now, just log the sorting request
	log.Printf("Sorting %d properties by %s (%s)", len(properties), sortBy, sortOrder)
}

// HTTP handler for vacant land search
func (engine *VacantLandSearchEngine) HandleVacantLandSearch(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var req VacantLandSearchRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, fmt.Sprintf("Invalid request body: %v", err), http.StatusBadRequest)
		return
	}

	response, err := engine.Search(req)
	if err != nil {
		http.Error(w, fmt.Sprintf("Search failed: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

	if err := json.NewEncoder(w).Encode(response); err != nil {
		log.Printf("Error encoding response: %v", err)
	}
}
