# Frontend-specific .gcloudignore for Next.js App Engine deployment
# This file excludes Go backend files and keeps only Next.js frontend files

# Go backend files (not needed for frontend)
*.go
go.mod
go.sum
main.go
propbolt
propbolt-brain
propbolthelper
cmd/
config/
database/
details/
propbolthelper/
realestate/
search/
utils/
zestimate/

# Backend configuration
app.yaml

# Development tools
.vscode/
.idea/
.history/
.internal/
*.swp
*.swo

# Version control
.git/
.gitignore

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Environment files
.env*

# Development directories
devtesting/
scripts/

# Documentation
README.md
*.md
platform*.png

# Deployment scripts
deploy*.sh
start-*.sh
Procfile
transaction.yaml
1.yaml
check-deployment-files.sh
test-deployment.sh

# Backend-specific ignore files
.gcloudignore-backend

# Temporary files
pids/
*.pid
*.seed
*.pid.lock
coverage/
*.lcov

# TypeScript build artifacts
*.tsbuildinfo
.eslintcache

# Test files
__tests__/
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# Cache directories
.cache/
.parcel-cache/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.production

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
