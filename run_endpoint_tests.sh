#!/bin/bash

# PropBolt RealEstateAPI Comprehensive Endpoint Testing Script
# Tests all endpoints with focus on vacant land search capabilities

set -e

echo "🚀 PropBolt RealEstateAPI Comprehensive Testing Suite"
echo "=" * 60
echo "📍 Target: Maximum Vacant Land Search Capability"
echo "🎯 Location: Daytona Beach, FL"
echo "🔑 API: RealEstateAPI.com"
echo ""

# Check if API key is set
if [ -z "$REAL_ESTATE_API_KEY" ]; then
    echo "❌ Error: REAL_ESTATE_API_KEY environment variable is not set"
    echo "Please set your RealEstateAPI.com API key:"
    echo "export REAL_ESTATE_API_KEY='your-api-key-here'"
    exit 1
fi

echo "✅ API Key found: ${REAL_ESTATE_API_KEY:0:10}..."
echo ""

# Set up test environment
PROJECT_DIR="/Users/<USER>/byte-media/v1-go"
TEST_DIR="$PROJECT_DIR/tests"
RESULTS_DIR="$PROJECT_DIR/test_results"

# Create directories if they don't exist
mkdir -p "$TEST_DIR"
mkdir -p "$RESULTS_DIR"

echo "📁 Test Directory: $TEST_DIR"
echo "📁 Results Directory: $RESULTS_DIR"
echo ""

# Set environment variables for testing
export REAL_ESTATE_API_URL="https://api.realestateapi.com"
export DATABASE_URL="**************************************************************************************************************************"
export PROXY_URLS="http://sp0o8xf1er:<EMAIL>:10001,http://sp0o8xf1er:<EMAIL>:10002"

# Test Configuration
TEST_LOCATION="Daytona Beach, FL"
TEST_ZIP="32114"
TEST_ADDRESS="123 Main Street, Daytona Beach, FL 32114"
TEST_LAT="29.2108"
TEST_LNG="-81.0228"

echo "🧪 Test Configuration:"
echo "   Location: $TEST_LOCATION"
echo "   ZIP Code: $TEST_ZIP"
echo "   Address: $TEST_ADDRESS"
echo "   Coordinates: $TEST_LAT, $TEST_LNG"
echo ""

# Function to run a single test
run_test() {
    local test_name="$1"
    local test_file="$2"
    local description="$3"
    
    echo "🔬 Running Test: $test_name"
    echo "📝 Description: $description"
    echo "📄 File: $test_file"
    
    start_time=$(date +%s)
    
    if [ -f "$test_file" ]; then
        echo "▶️  Executing test..."
        
        # Run the test and capture output
        if go run "$test_file" > "$RESULTS_DIR/${test_name}_output.log" 2>&1; then
            end_time=$(date +%s)
            duration=$((end_time - start_time))
            echo "✅ Test completed successfully in ${duration}s"
            echo "📊 Results saved to: $RESULTS_DIR/${test_name}_output.log"
        else
            end_time=$(date +%s)
            duration=$((end_time - start_time))
            echo "❌ Test failed after ${duration}s"
            echo "📊 Error log saved to: $RESULTS_DIR/${test_name}_output.log"
            echo "🔍 Last 10 lines of error log:"
            tail -10 "$RESULTS_DIR/${test_name}_output.log"
        fi
    else
        echo "❌ Test file not found: $test_file"
    fi
    
    echo ""
}

# Function to test individual endpoints
test_individual_endpoints() {
    echo "🎯 PHASE 1: Individual Endpoint Testing"
    echo "=" * 40
    
    # Test 1: AutoComplete
    echo "1️⃣ Testing AutoComplete Endpoint"
    cat > "$TEST_DIR/test_autocomplete.go" << 'EOF'
package main

import (
    "fmt"
    "log"
    "os"
    "propbolt/realestate"
)

func main() {
    client := realestate.NewClient()
    
    testInputs := []string{
        "Daytona Beach, FL",
        "32114",
        "Florida",
        "123 Main Street, Daytona Beach",
    }
    
    for _, input := range testInputs {
        fmt.Printf("Testing AutoComplete with: %s\n", input)
        response, err := client.AutoComplete(input)
        if err != nil {
            log.Printf("Error: %v", err)
        } else {
            fmt.Printf("Results: %d suggestions\n", len(response.Results))
            for i, result := range response.Results {
                if i < 3 { // Show first 3 results
                    fmt.Printf("  - %s (%s)\n", result.DisplayText, result.SearchType)
                }
            }
        }
        fmt.Println()
    }
}
EOF
    
    run_test "autocomplete" "$TEST_DIR/test_autocomplete.go" "Location autocomplete and suggestion testing"
    
    # Test 2: Property Search (Vacant Land Focus)
    echo "2️⃣ Testing Property Search - Vacant Land Focus"
    cat > "$TEST_DIR/test_property_search.go" << 'EOF'
package main

import (
    "fmt"
    "log"
    "propbolt/realestate"
)

func main() {
    client := realestate.NewClient()
    
    // Test Case 1: Basic vacant land search
    query1 := map[string]interface{}{
        "city":         "Daytona Beach",
        "state":        "FL",
        "propertyType": "land",
        "listingStatus": "active",
        "minPrice":     10000,
        "maxPrice":     500000,
    }
    
    fmt.Println("Testing Basic Vacant Land Search:")
    response1, err := client.PropertySearch(query1, 25, 0, false)
    if err != nil {
        log.Printf("Error: %v", err)
    } else {
        fmt.Printf("Found %d properties\n", len(response1.Properties))
        landCount := 0
        for _, prop := range response1.Properties {
            if prop.PropertyInfo != nil {
                if propType, ok := prop.PropertyInfo["propertyType"].(string); ok {
                    if propType == "land" || propType == "vacant_land" {
                        landCount++
                    }
                }
            }
        }
        fmt.Printf("Vacant land properties: %d\n", landCount)
    }
    
    // Test Case 2: Advanced vacant land search with utilities
    query2 := map[string]interface{}{
        "zipCode":      "32114",
        "propertyType": "land",
        "minAcres":     0.5,
        "maxAcres":     5.0,
        "hasUtilities": true,
        "isBuildable":  true,
    }
    
    fmt.Println("\nTesting Advanced Vacant Land Search:")
    response2, err := client.PropertySearch(query2, 25, 0, false)
    if err != nil {
        log.Printf("Error: %v", err)
    } else {
        fmt.Printf("Found %d buildable properties with utilities\n", len(response2.Properties))
    }
}
EOF
    
    run_test "property_search" "$TEST_DIR/test_property_search.go" "Comprehensive vacant land property search testing"
    
    # Test 3: Property Mapping
    echo "3️⃣ Testing Property Mapping"
    cat > "$TEST_DIR/test_property_mapping.go" << 'EOF'
package main

import (
    "fmt"
    "log"
    "propbolt/realestate"
)

func main() {
    client := realestate.NewClient()
    
    query := map[string]interface{}{
        "city":         "Daytona Beach",
        "state":        "FL",
        "propertyType": "land",
        "bounds": map[string]float64{
            "north": 29.3,
            "south": 29.1,
            "east":  -80.9,
            "west":  -81.1,
        },
    }
    
    fmt.Println("Testing Property Mapping for Daytona Beach area:")
    response, err := client.PropertyMapping(query)
    if err != nil {
        log.Printf("Error: %v", err)
    } else {
        fmt.Printf("Found %d properties on map\n", len(response.Properties))
        fmt.Printf("Total properties in area: %d\n", response.Total)
        
        // Show sample properties
        for i, prop := range response.Properties {
            if i < 5 { // Show first 5
                fmt.Printf("  Property %d: %s (%.6f, %.6f)\n", 
                    i+1, prop.Address, prop.Latitude, prop.Longitude)
            }
        }
    }
}
EOF
    
    run_test "property_mapping" "$TEST_DIR/test_property_mapping.go" "Geographic property mapping and bounds testing"
    
    # Test 4: Property Detail
    echo "4️⃣ Testing Property Detail"
    cat > "$TEST_DIR/test_property_detail.go" << 'EOF'
package main

import (
    "fmt"
    "log"
    "propbolt/realestate"
)

func main() {
    client := realestate.NewClient()
    
    testAddress := "123 Main Street, Daytona Beach, FL 32114"
    
    fmt.Printf("Testing Property Detail for: %s\n", testAddress)
    response, err := client.PropertyDetail(testAddress, "", true)
    if err != nil {
        log.Printf("Error: %v", err)
    } else {
        prop := response.Property
        fmt.Printf("Property ID: %s\n", prop.ID)
        fmt.Printf("Address: %s\n", prop.Address)
        fmt.Printf("City: %s, State: %s\n", prop.City, prop.State)
        fmt.Printf("Coordinates: %.6f, %.6f\n", prop.Latitude, prop.Longitude)
        
        if prop.PropertyInfo != nil {
            fmt.Println("Property Information:")
            for key, value := range prop.PropertyInfo {
                fmt.Printf("  %s: %v\n", key, value)
            }
        }
        
        if prop.LotInfo != nil {
            fmt.Println("Lot Information:")
            for key, value := range prop.LotInfo {
                fmt.Printf("  %s: %v\n", key, value)
            }
        }
        
        fmt.Printf("Comparables found: %d\n", len(prop.Comps))
    }
}
EOF
    
    run_test "property_detail" "$TEST_DIR/test_property_detail.go" "Detailed property information and comparables testing"
}

# Function to test vacant land specific scenarios
test_vacant_land_scenarios() {
    echo "🏞️ PHASE 2: Vacant Land Specific Testing"
    echo "=" * 40
    
    # Test comprehensive vacant land search
    echo "🎯 Testing Comprehensive Vacant Land Search Engine"
    cat > "$TEST_DIR/test_vacant_land_comprehensive.go" << 'EOF'
package main

import (
    "encoding/json"
    "fmt"
    "log"
    "propbolt/realestate"
)

func main() {
    client := realestate.NewClient()
    
    // Comprehensive vacant land search scenarios
    scenarios := []struct {
        name  string
        query map[string]interface{}
    }{
        {
            "Affordable Vacant Land",
            map[string]interface{}{
                "city":         "Daytona Beach",
                "state":        "FL",
                "propertyType": "land",
                "minPrice":     5000,
                "maxPrice":     50000,
                "minLotSize":   2000,
            },
        },
        {
            "Buildable Residential Land",
            map[string]interface{}{
                "zipCode":      "32114",
                "propertyType": "land",
                "landUse":      "residential",
                "isBuildable":  true,
                "hasUtilities": true,
                "hasRoadAccess": true,
            },
        },
        {
            "Waterfront Land",
            map[string]interface{}{
                "city":         "Daytona Beach",
                "state":        "FL",
                "propertyType": "land",
                "isWaterfront": true,
                "minAcres":     0.25,
            },
        },
        {
            "Commercial Land",
            map[string]interface{}{
                "city":         "Daytona Beach",
                "state":        "FL",
                "propertyType": "land",
                "landUse":      "commercial",
                "minLotSize":   10000,
            },
        },
        {
            "Large Acreage",
            map[string]interface{}{
                "county":       "Volusia",
                "state":        "FL",
                "propertyType": "land",
                "minAcres":     5.0,
                "maxAcres":     50.0,
            },
        },
    }
    
    totalLandFound := 0
    
    for _, scenario := range scenarios {
        fmt.Printf("\n🔍 Testing Scenario: %s\n", scenario.name)
        fmt.Printf("Query: %s\n", formatQuery(scenario.query))
        
        response, err := client.PropertySearch(scenario.query, 25, 0, false)
        if err != nil {
            log.Printf("❌ Error: %v", err)
            continue
        }
        
        landCount := countVacantLand(response.Properties)
        totalLandFound += landCount
        
        fmt.Printf("✅ Found %d total properties, %d vacant land\n", 
            len(response.Properties), landCount)
        
        // Show sample properties
        if len(response.Properties) > 0 {
            fmt.Println("Sample properties:")
            for i, prop := range response.Properties {
                if i >= 3 { break }
                fmt.Printf("  - %s ($%.0f)\n", prop.Address, prop.Price)
            }
        }
    }
    
    fmt.Printf("\n🎉 TOTAL VACANT LAND FOUND: %d properties\n", totalLandFound)
}

func formatQuery(query map[string]interface{}) string {
    jsonData, _ := json.Marshal(query)
    return string(jsonData)
}

func countVacantLand(properties []PropertySearchResult) int {
    count := 0
    for _, prop := range properties {
        if prop.PropertyInfo != nil {
            if propType, ok := prop.PropertyInfo["propertyType"].(string); ok {
                if propType == "land" || propType == "vacant_land" || propType == "lot" {
                    count++
                }
            }
        }
    }
    return count
}
EOF
    
    run_test "vacant_land_comprehensive" "$TEST_DIR/test_vacant_land_comprehensive.go" "Comprehensive vacant land search scenarios"
}

# Function to test performance and reliability
test_performance() {
    echo "⚡ PHASE 3: Performance and Reliability Testing"
    echo "=" * 40
    
    echo "📊 Testing API Performance and Response Times"
    cat > "$TEST_DIR/test_performance.go" << 'EOF'
package main

import (
    "fmt"
    "log"
    "time"
    "propbolt/realestate"
)

func main() {
    client := realestate.NewClient()
    
    // Performance test scenarios
    tests := []struct {
        name     string
        testFunc func() error
    }{
        {
            "AutoComplete Performance",
            func() error {
                _, err := client.AutoComplete("Daytona Beach, FL")
                return err
            },
        },
        {
            "Property Search Performance",
            func() error {
                query := map[string]interface{}{
                    "city":         "Daytona Beach",
                    "state":        "FL",
                    "propertyType": "land",
                }
                _, err := client.PropertySearch(query, 25, 0, false)
                return err
            },
        },
        {
            "Property Mapping Performance",
            func() error {
                query := map[string]interface{}{
                    "city":         "Daytona Beach",
                    "state":        "FL",
                    "propertyType": "land",
                }
                _, err := client.PropertyMapping(query)
                return err
            },
        },
    }
    
    for _, test := range tests {
        fmt.Printf("🏃 Running %s...\n", test.name)
        
        // Run test 3 times and average
        var totalDuration time.Duration
        successCount := 0
        
        for i := 0; i < 3; i++ {
            start := time.Now()
            err := test.testFunc()
            duration := time.Since(start)
            
            if err != nil {
                log.Printf("  Attempt %d failed: %v", i+1, err)
            } else {
                totalDuration += duration
                successCount++
                fmt.Printf("  Attempt %d: %.2fs\n", i+1, duration.Seconds())
            }
        }
        
        if successCount > 0 {
            avgDuration := totalDuration / time.Duration(successCount)
            fmt.Printf("✅ %s: %.2fs average (success rate: %d/3)\n", 
                test.name, avgDuration.Seconds(), successCount)
        } else {
            fmt.Printf("❌ %s: All attempts failed\n", test.name)
        }
        fmt.Println()
    }
}
EOF
    
    run_test "performance" "$TEST_DIR/test_performance.go" "API performance and response time testing"
}

# Main execution
main() {
    echo "🎬 Starting Comprehensive Endpoint Testing..."
    echo ""
    
    # Phase 1: Individual endpoint testing
    test_individual_endpoints
    
    # Phase 2: Vacant land specific scenarios
    test_vacant_land_scenarios
    
    # Phase 3: Performance testing
    test_performance
    
    echo "🎉 ALL TESTS COMPLETED!"
    echo ""
    echo "📊 Test Results Summary:"
    echo "📁 Results Directory: $RESULTS_DIR"
    echo "📄 Log Files:"
    ls -la "$RESULTS_DIR"/*.log 2>/dev/null || echo "No log files found"
    echo ""
    echo "🔍 To view detailed results:"
    echo "cat $RESULTS_DIR/[test_name]_output.log"
    echo ""
    echo "✅ PropBolt RealEstateAPI testing complete!"
    echo "🏞️ Vacant land search capabilities validated"
    echo "🎯 Ready for maximum land search functionality"
}

# Execute main function
main
