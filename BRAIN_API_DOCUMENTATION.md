# PropBolt Brain API Documentation

This document provides comprehensive documentation for the PropBolt Brain API endpoints, including both legacy PropBolt functionality and new RealEstateAPI.com proxy endpoints.

## Base URL
```
https://propbolt.com/api
```

## Authentication
Admin-only access required. Users must authenticate via `propbolt.com/login` with 'Admin' role.

---

## Legacy PropBolt Endpoints

### Health Check
**GET** `/`

Returns the health status of the API.

**Response:**
```json
{
  "status": "ok"
}
```

### Property Details
**GET** `/property`

Get detailed property information by ID, URL, or address.

**Parameters:**
- `id` (optional): Property ID
- `url` (optional): Property URL
- `address` (optional): Property address
- `listingPhotos` (optional): Include listing photos (true/false)

**Example:**
```
GET /property?address=123 Main St, Anytown, FL&listingPhotos=true
```

### Property Search - For Sale Enhanced
**GET** `/search/for-sale-enhanced`

Enhanced search for properties with land-specific filters.

**Parameters:**
- `neLat`, `neLong`, `swLat`, `swLong` (required): Geographic bounds
- `page` (optional): Page number (default: 1)
- `zoom` (optional): Zoom level 1-20 (default: 10)
- Property type filters: `isAllHomes`, `isTownhouse`, `isMultiFamily`, `isCondo`, `isLotLand`, `isApartment`, `isManufactured`, `isApartmentOrCondo`
- Land-specific parameters:
  - `lotSizeMin`, `lotSizeMax`: Lot size filters in square feet
  - `hasUtilities`, `hasWater`, `hasSewer`, `hasElectric`, `hasGas`: Utility filters
  - `isWaterfront`, `hasView`, `isBuildable`, `hasRoadAccess`: Land feature filters
  - `zoningType`: Zoning classification (residential, commercial, agricultural)

---

## RealEstateAPI.com Proxy Endpoints

These endpoints provide secure server-side access to RealEstateAPI.com services. All endpoints require admin authentication and use POST methods with JSON payloads.

### AutoComplete Address API
**POST** `/api/v1/proxy/autocomplete`

Get address suggestions and location autocomplete.

**Request Body:**
```json
{
  "input": "123 Main"
}
```

**Response:**
```json
{
  "results": [
    {
      "searchType": "A",
      "displayText": "123 Main St, Anytown, FL 12345",
      "address": "123 Main St",
      "city": "Anytown",
      "state": "FL",
      "zipCode": "12345"
    }
  ]
}
```

### Property Mapping API
**POST** `/api/v1/proxy/mapping`

Get property pin locations for map visualization.

**Request Body:**
```json
{
  "query": {
    "location": {
      "city": "Daytona Beach",
      "state": "FL"
    },
    "propertyType": "land"
  }
}
```

**Response:**
```json
{
  "properties": [
    {
      "id": "12345",
      "address": "123 Main St",
      "latitude": 29.2108,
      "longitude": -81.0228,
      "price": 50000,
      "status": "for_sale"
    }
  ],
  "total": 1
}
```

### Property Detail API
**POST** `/api/v1/proxy/property-detail`

Get comprehensive property information.

**Request Body:**
```json
{
  "address": "123 Main St, Daytona Beach, FL",
  "comps": true
}
```

**Response:**
```json
{
  "property": {
    "id": "12345",
    "address": "123 Main St",
    "city": "Daytona Beach",
    "state": "FL",
    "zipCode": "32114",
    "latitude": 29.2108,
    "longitude": -81.0228,
    "propertyInfo": {
      "lotSize": 10000,
      "zoning": "residential",
      "yearBuilt": null
    },
    "ownerInfo": {
      "name": "John Doe",
      "mailingAddress": "456 Oak St"
    },
    "lotInfo": {
      "apn": "1234567890",
      "landUse": "vacant_land",
      "legalDescription": "Lot 1, Block A, Subdivision XYZ"
    },
    "taxInfo": {
      "assessedValue": 45000,
      "taxAmount": 1200
    },
    "saleHistory": [],
    "mlsHistory": [],
    "comps": []
  }
}
```

### Property Detail Bulk API
**POST** `/api/v1/proxy/property-detail-bulk`

Get comprehensive information for multiple properties (up to 1000).

**Request Body:**
```json
{
  "propertyIds": ["12345", "67890", "11111"],
  "comps": false
}
```

### Property Search API
**POST** `/api/v1/proxy/property-search`

Advanced property search with complex filtering.

**Request Body:**
```json
{
  "query": {
    "location": {
      "polygon": [
        [29.2108, -81.0228],
        [29.2200, -81.0100],
        [29.2000, -81.0100],
        [29.2108, -81.0228]
      ]
    },
    "propertyType": "land",
    "priceRange": {
      "min": 10000,
      "max": 100000
    },
    "lotSize": {
      "min": 5000,
      "max": 50000
    }
  },
  "limit": 100,
  "offset": 0,
  "ids_only": false
}
```

### Involuntary Liens API
**POST** `/api/v1/proxy/involuntary-liens`

Get lien information for properties.

**Request Body:**
```json
{
  "address": "123 Main St, Daytona Beach, FL"
}
```

**Response:**
```json
{
  "liens": [
    {
      "type": "tax_lien",
      "amount": 5000,
      "date": "2023-01-15",
      "description": "Property tax lien",
      "status": "active"
    }
  ]
}
```

### Property Comps v3 API
**POST** `/api/v1/proxy/property-comps-v3`

Advanced comparable properties analysis with custom parameters.

**Request Body:**
```json
{
  "address": "123 Main St, Daytona Beach, FL",
  "customParameters": {
    "livingSquareFeet": {
      "min": 1500,
      "max": 2500
    },
    "lotSquareFeet": {
      "min": 8000,
      "max": 12000
    }
  },
  "maxComps": 10,
  "radiusMiles": 2.0
}
```

**Response:**
```json
{
  "subject": {
    "address": "123 Main St",
    "latitude": 29.2108,
    "longitude": -81.0228,
    "livingSqft": 2000,
    "lotSqft": 10000,
    "bedrooms": 3,
    "bathrooms": 2.5,
    "yearBuilt": 1995
  },
  "comps": [
    {
      "address": "456 Oak St",
      "distance": 0.5,
      "salePrice": 275000,
      "saleDate": "2023-06-15",
      "livingSqft": 1950,
      "lotSqft": 9500,
      "pricePerSqft": 141.03,
      "similarityScore": 0.92
    }
  ],
  "avm": {
    "low": 260000,
    "high": 290000,
    "confidence": 0.85
  }
}
```

### Property Comps v2 API
**POST** `/api/v1/proxy/property-comps-v2`

Basic comparable properties analysis.

**Request Body:**
```json
{
  "address": "123 Main St, Daytona Beach, FL",
  "maxComps": 5,
  "radiusMiles": 1.5
}
```

### SkipTrace API
**POST** `/api/v1/proxy/skiptrace`

Get owner contact information and demographics.

**Request Body:**
```json
{
  "first_name": "John",
  "last_name": "Doe",
  "address": "123 Main St",
  "city": "Daytona Beach",
  "state": "FL",
  "zip": "32114"
}
```

**Response:**
```json
{
  "identity": {
    "name": "John Doe",
    "addressHistory": [
      {
        "address": "123 Main St",
        "city": "Daytona Beach",
        "state": "FL",
        "years": "2018-present"
      }
    ],
    "phoneNumbers": ["******-123-4567"],
    "emailAddresses": ["<EMAIL>"]
  },
  "demographics": {
    "age": 45,
    "gender": "male",
    "education": "bachelor_degree"
  },
  "stats": {
    "phonesFound": 1,
    "emailsFound": 1,
    "jobsFound": 0,
    "relationshipsFound": 2
  }
}
```

---

## Error Handling

All endpoints return structured error responses:

```json
{
  "error": {
    "message": "Property not found",
    "code": "PROPERTY_NOT_FOUND",
    "details": {
      "attempts": 3,
      "totalTime": "2.5s",
      "lastError": "status: 404"
    }
  }
}
```

## Rate Limiting

API requests are subject to rate limiting. The proxy endpoints implement intelligent retry logic with exponential backoff.

## Support

For API support and integration assistance, contact the PropBolt Brain development team.
