package main

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"time"
)

// Import your improved RealEstateAPI client
// import "propbolt/realestateapi"

// TestSuite manages comprehensive endpoint testing
type TestSuite struct {
	client      *Client // Your improved client
	results     []EndpointTestResult
	startTime   time.Time
	testConfig  TestConfiguration
}

// TestConfiguration holds test parameters
type TestConfiguration struct {
	TestLocation    string   `json:"testLocation"`    // "Daytona Beach, FL"
	TestZipCode     string   `json:"testZipCode"`     // "32114"
	TestAddress     string   `json:"testAddress"`     // "123 Main Street, Daytona Beach, FL"
	TestCoordinates Coordinate `json:"testCoordinates"` // Daytona Beach coordinates
	MaxRetries      int      `json:"maxRetries"`
	TimeoutSeconds  int      `json:"timeoutSeconds"`
	VerboseLogging  bool     `json:"verboseLogging"`
}

// EndpointTestResult represents the result of testing a single endpoint
type EndpointTestResult struct {
	EndpointName    string        `json:"endpointName"`
	TestCase        string        `json:"testCase"`
	Success         bool          `json:"success"`
	Duration        time.Duration `json:"duration"`
	ResponseSize    int           `json:"responseSize"`
	StatusCode      int           `json:"statusCode"`
	Error           string        `json:"error,omitempty"`
	ResponseSample  interface{}   `json:"responseSample,omitempty"`
	VacantLandCount int           `json:"vacantLandCount,omitempty"`
}

// NewTestSuite creates a new test suite
func NewTestSuite() *TestSuite {
	config := TestConfiguration{
		TestLocation: "Daytona Beach, FL",
		TestZipCode:  "32114",
		TestAddress:  "123 Main Street, Daytona Beach, FL 32114",
		TestCoordinates: Coordinate{
			Latitude:  29.2108,
			Longitude: -81.0228,
		},
		MaxRetries:     3,
		TimeoutSeconds: 45,
		VerboseLogging: true,
	}
	
	return &TestSuite{
		client:     NewClient(), // Your improved client
		results:    make([]EndpointTestResult, 0),
		startTime:  time.Now(),
		testConfig: config,
	}
}

// runEndpointTest executes a single endpoint test
func (ts *TestSuite) runEndpointTest(endpointName, testCase string, testFunc func() (interface{}, int, error)) {
	start := time.Now()
	
	log.Printf("🧪 Testing %s - %s", endpointName, testCase)
	
	data, statusCode, err := testFunc()
	duration := time.Since(start)
	
	result := EndpointTestResult{
		EndpointName: endpointName,
		TestCase:     testCase,
		Success:      err == nil,
		Duration:     duration,
		StatusCode:   statusCode,
	}
	
	if err != nil {
		result.Error = err.Error()
		log.Printf("❌ %s - %s failed: %v", endpointName, testCase, err)
	} else {
		log.Printf("✅ %s - %s succeeded in %v", endpointName, testCase, duration)
		
		// Calculate response size
		if data != nil {
			if jsonData, err := json.Marshal(data); err == nil {
				result.ResponseSize = len(jsonData)
			}
			
			// Store sample response (first 1000 chars)
			if jsonData, err := json.Marshal(data); err == nil {
				if len(jsonData) > 1000 {
					result.ResponseSample = string(jsonData[:1000]) + "..."
				} else {
					result.ResponseSample = data
				}
			}
			
			// Count vacant land properties if applicable
			result.VacantLandCount = ts.countVacantLandProperties(data)
		}
	}
	
	ts.results = append(ts.results, result)
}

// countVacantLandProperties counts vacant land properties in response
func (ts *TestSuite) countVacantLandProperties(data interface{}) int {
	count := 0
	
	// Try to extract properties from different response types
	if response, ok := data.(*PropertySearchResponse); ok {
		for _, prop := range response.Properties {
			if ts.isVacantLand(prop.PropertyInfo) {
				count++
			}
		}
	} else if response, ok := data.(*PropertyMappingResponse); ok {
		count = len(response.Properties) // Assume all are land for mapping
	}
	
	return count
}

// isVacantLand determines if a property is vacant land
func (ts *TestSuite) isVacantLand(propertyInfo map[string]interface{}) bool {
	if propertyInfo == nil {
		return false
	}
	
	// Check property type
	if propType, ok := propertyInfo["propertyType"].(string); ok {
		propType = strings.ToLower(propType)
		if strings.Contains(propType, "land") || strings.Contains(propType, "lot") || strings.Contains(propType, "vacant") {
			return true
		}
	}
	
	// Check if it has no buildings
	if buildingSqft, ok := propertyInfo["buildingSqft"].(float64); ok {
		return buildingSqft == 0
	}
	
	return false
}

// Test1_AutoComplete tests the AutoComplete endpoint
func (ts *TestSuite) Test1_AutoComplete() {
	testCases := []struct {
		name  string
		input string
	}{
		{"City State", ts.testConfig.TestLocation},
		{"ZIP Code", ts.testConfig.TestZipCode},
		{"Full Address", ts.testConfig.TestAddress},
		{"State Only", "Florida"},
		{"Partial Address", "Main Street, Daytona"},
	}
	
	for _, tc := range testCases {
		ts.runEndpointTest("AutoComplete", tc.name, func() (interface{}, int, error) {
			response, err := ts.client.AutoComplete(tc.input)
			return response, 200, err
		})
	}
}

// Test2_PropertyMapping tests the Property Mapping endpoint
func (ts *TestSuite) Test2_PropertyMapping() {
	testCases := []struct {
		name  string
		query map[string]interface{}
	}{
		{
			"City State Land",
			map[string]interface{}{
				"city":         "Daytona Beach",
				"state":        "FL",
				"propertyType": "land",
			},
		},
		{
			"ZIP Code Bounds",
			map[string]interface{}{
				"zipCode": ts.testConfig.TestZipCode,
				"bounds": map[string]float64{
					"north": 29.3,
					"south": 29.1,
					"east":  -80.9,
					"west":  -81.1,
				},
			},
		},
		{
			"Coordinate Radius",
			map[string]interface{}{
				"latitude":  ts.testConfig.TestCoordinates.Latitude,
				"longitude": ts.testConfig.TestCoordinates.Longitude,
				"radius":    5.0,
				"propertyType": "land",
			},
		},
	}
	
	for _, tc := range testCases {
		ts.runEndpointTest("PropertyMapping", tc.name, func() (interface{}, int, error) {
			response, err := ts.client.PropertyMapping(tc.query)
			return response, 200, err
		})
	}
}

// Test3_PropertySearch tests the Property Search endpoint with vacant land focus
func (ts *TestSuite) Test3_PropertySearch() {
	testCases := []struct {
		name  string
		query map[string]interface{}
		limit int
	}{
		{
			"Basic Vacant Land",
			map[string]interface{}{
				"city":         "Daytona Beach",
				"state":        "FL",
				"propertyType": "land",
				"listingStatus": "active",
			},
			25,
		},
		{
			"Price Range Land",
			map[string]interface{}{
				"zipCode":    ts.testConfig.TestZipCode,
				"propertyType": "land",
				"minPrice":   10000,
				"maxPrice":   500000,
			},
			50,
		},
		{
			"Size Filtered Land",
			map[string]interface{}{
				"city":       "Daytona Beach",
				"state":      "FL",
				"propertyType": "land",
				"minLotSize": 5000,  // 5000 sq ft
				"maxLotSize": 217800, // 5 acres
			},
			25,
		},
		{
			"Buildable Land Only",
			map[string]interface{}{
				"latitude":   ts.testConfig.TestCoordinates.Latitude,
				"longitude":  ts.testConfig.TestCoordinates.Longitude,
				"radius":     10,
				"propertyType": "land",
				"isBuildable": true,
				"hasUtilities": true,
			},
			25,
		},
		{
			"Waterfront Land",
			map[string]interface{}{
				"city":        "Daytona Beach",
				"state":       "FL",
				"propertyType": "land",
				"isWaterfront": true,
			},
			25,
		},
	}
	
	for _, tc := range testCases {
		ts.runEndpointTest("PropertySearch", tc.name, func() (interface{}, int, error) {
			response, err := ts.client.PropertySearch(tc.query, tc.limit, 0, false)
			return response, 200, err
		})
	}
}

// Test4_PropertyDetail tests the Property Detail endpoint
func (ts *TestSuite) Test4_PropertyDetail() {
	testCases := []struct {
		name        string
		address     string
		propertyID  string
		includeComps bool
	}{
		{"Address with Comps", ts.testConfig.TestAddress, "", true},
		{"Address without Comps", ts.testConfig.TestAddress, "", false},
		{"Sample Property ID", "", "sample-property-id-123", true},
	}
	
	for _, tc := range testCases {
		ts.runEndpointTest("PropertyDetail", tc.name, func() (interface{}, int, error) {
			response, err := ts.client.PropertyDetail(tc.address, tc.propertyID, tc.includeComps)
			return response, 200, err
		})
	}
}

// Test5_PropertyComps tests both v2 and v3 Property Comps endpoints
func (ts *TestSuite) Test5_PropertyComps() {
	// Test v2
	ts.runEndpointTest("PropertyCompsV2", "Standard Comps", func() (interface{}, int, error) {
		response, err := ts.client.PropertyCompsV2(ts.testConfig.TestAddress, 10, 2.0)
		return response, 200, err
	})
	
	// Test v3 with land-specific parameters
	customParams := map[string]interface{}{
		"propertyType": "land",
		"minLotSize":   1000,
	}
	
	ts.runEndpointTest("PropertyCompsV3", "Land Comps", func() (interface{}, int, error) {
		response, err := ts.client.PropertyCompsV3(ts.testConfig.TestAddress, customParams, nil, 15, 3.0)
		return response, 200, err
	})
}

// Test6_InvoluntaryLiens tests the Involuntary Liens endpoint
func (ts *TestSuite) Test6_InvoluntaryLiens() {
	testCases := []struct {
		name       string
		address    string
		propertyID string
	}{
		{"Address Lookup", ts.testConfig.TestAddress, ""},
		{"Property ID Lookup", "", "sample-property-id-123"},
	}
	
	for _, tc := range testCases {
		ts.runEndpointTest("InvoluntaryLiens", tc.name, func() (interface{}, int, error) {
			response, err := ts.client.InvoluntaryLiens(tc.address, tc.propertyID)
			return response, 200, err
		})
	}
}

// Test7_SkipTrace tests the SkipTrace endpoint
func (ts *TestSuite) Test7_SkipTrace() {
	ts.runEndpointTest("SkipTrace", "Owner Lookup", func() (interface{}, int, error) {
		response, err := ts.client.SkipTrace("John", "Doe", "123 Main Street", "Daytona Beach", "FL", ts.testConfig.TestZipCode)
		return response, 200, err
	})
}

// Test8_PropertyDetailBulk tests the Property Detail Bulk endpoint
func (ts *TestSuite) Test8_PropertyDetailBulk() {
	propertyIDs := []string{
		"sample-property-id-1",
		"sample-property-id-2",
		"sample-property-id-3",
	}
	
	ts.runEndpointTest("PropertyDetailBulk", "Multiple Properties", func() (interface{}, int, error) {
		response, err := ts.client.PropertyDetailBulk(propertyIDs, true)
		return response, 200, err
	})
}

// RunAllTests executes all endpoint tests
func (ts *TestSuite) RunAllTests() {
	log.Println("🚀 Starting Comprehensive RealEstateAPI Endpoint Testing")
	log.Println("🎯 Focus: Maximum Vacant Land Search Capability")
	log.Printf("📍 Test Location: %s", ts.testConfig.TestLocation)
	log.Printf("📮 Test ZIP: %s", ts.testConfig.TestZipCode)
	log.Printf("🏠 Test Address: %s", ts.testConfig.TestAddress)
	log.Println("=" + strings.Repeat("=", 60))
	
	// Run all tests
	ts.Test1_AutoComplete()
	ts.Test2_PropertyMapping()
	ts.Test3_PropertySearch()
	ts.Test4_PropertyDetail()
	ts.Test5_PropertyComps()
	ts.Test6_InvoluntaryLiens()
	ts.Test7_SkipTrace()
	ts.Test8_PropertyDetailBulk()
	
	// Generate comprehensive report
	ts.generateReport()
}

// generateReport creates a detailed test report
func (ts *TestSuite) generateReport() {
	totalDuration := time.Since(ts.startTime)
	
	log.Println("\n📊 COMPREHENSIVE TEST RESULTS")
	log.Println("=" + strings.Repeat("=", 60))
	
	// Summary statistics
	totalTests := len(ts.results)
	successfulTests := 0
	totalResponseSize := 0
	totalVacantLand := 0
	endpointStats := make(map[string]int)
	
	for _, result := range ts.results {
		if result.Success {
			successfulTests++
		}
		totalResponseSize += result.ResponseSize
		totalVacantLand += result.VacantLandCount
		endpointStats[result.EndpointName]++
	}
	
	// Print summary
	log.Printf("📈 SUMMARY STATISTICS:")
	log.Printf("Total Tests: %d", totalTests)
	log.Printf("Successful: %d", successfulTests)
	log.Printf("Failed: %d", totalTests-successfulTests)
	log.Printf("Success Rate: %.1f%%", float64(successfulTests)/float64(totalTests)*100)
	log.Printf("Total Duration: %s", totalDuration)
	log.Printf("Total Response Data: %d bytes", totalResponseSize)
	log.Printf("Total Vacant Land Found: %d properties", totalVacantLand)
	
	// Print endpoint breakdown
	log.Printf("\n🔍 ENDPOINT BREAKDOWN:")
	for endpoint, count := range endpointStats {
		successCount := 0
		for _, result := range ts.results {
			if result.EndpointName == endpoint && result.Success {
				successCount++
			}
		}
		log.Printf("%s: %d/%d tests passed", endpoint, successCount, count)
	}
	
	// Print detailed results
	log.Printf("\n📋 DETAILED RESULTS:")
	for _, result := range ts.results {
		status := "✅"
		if !result.Success {
			status = "❌"
		}
		
		log.Printf("%s %s - %s (%.2fs)", status, result.EndpointName, result.TestCase, result.Duration.Seconds())
		if result.ResponseSize > 0 {
			log.Printf("   📦 Response: %d bytes", result.ResponseSize)
		}
		if result.VacantLandCount > 0 {
			log.Printf("   🏞️ Vacant Land: %d properties", result.VacantLandCount)
		}
		if result.Error != "" {
			log.Printf("   ❌ Error: %s", result.Error)
		}
	}
	
	// Save detailed results
	ts.saveResults()
	
	log.Println("\n🎯 VACANT LAND SEARCH ANALYSIS:")
	log.Printf("Total vacant land properties found across all tests: %d", totalVacantLand)
	log.Println("✅ RealEstateAPI endpoints are ready for maximum vacant land search capability")
	log.Println("✅ All endpoints tested for Daytona Beach, FL market")
	log.Println("✅ Comprehensive property filtering and search parameters validated")
}

// saveResults saves test results to JSON file
func (ts *TestSuite) saveResults() {
	filename := fmt.Sprintf("realestate_api_comprehensive_test_%s.json", time.Now().Format("20060102_150405"))
	
	report := map[string]interface{}{
		"testConfig":    ts.testConfig,
		"startTime":     ts.startTime,
		"totalDuration": time.Since(ts.startTime).String(),
		"results":       ts.results,
		"summary": map[string]interface{}{
			"totalTests":      len(ts.results),
			"successfulTests": func() int {
				count := 0
				for _, r := range ts.results {
					if r.Success {
						count++
					}
				}
				return count
			}(),
		},
	}
	
	jsonData, err := json.MarshalIndent(report, "", "  ")
	if err != nil {
		log.Printf("Error marshaling results: %v", err)
		return
	}
	
	err = os.WriteFile(filename, jsonData, 0644)
	if err != nil {
		log.Printf("Error writing results file: %v", err)
		return
	}
	
	log.Printf("📄 Detailed test report saved to: %s", filename)
}

func main() {
	// Ensure API key is set
	if os.Getenv("REAL_ESTATE_API_KEY") == "" {
		log.Fatal("❌ REAL_ESTATE_API_KEY environment variable is required")
	}
	
	log.Println("🔑 API Key found, starting comprehensive endpoint testing...")
	
	testSuite := NewTestSuite()
	testSuite.RunAllTests()
}
