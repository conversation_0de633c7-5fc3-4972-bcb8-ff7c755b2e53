/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: [
      'propbolt.com',
      'api.mapbox.com'
    ],
  },
  env: {
    NEXT_PUBLIC_API_BASE_URL: 'https://propbolt.com'
  },
  async rewrites() {
    // API routing to propbolt.com backend
    const apiBaseUrl = 'https://propbolt.com';

    return [
      {
        source: '/api/:path*',
        destination: `${apiBaseUrl}/api/:path*`,
      },
    ];
  },
}

module.exports = nextConfig
