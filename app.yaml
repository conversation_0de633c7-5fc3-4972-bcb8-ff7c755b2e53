runtime: go122
service: default

# Environment variables
env_variables:
  PORT: "8080"
  PROXY_URLS: "http://sp0o8xf1er:<EMAIL>:10001,http://sp0o8xf1er:<EMAIL>:10002,http://sp0o8xf1er:<EMAIL>:10003,http://sp0o8xf1er:<EMAIL>:10004,http://sp0o8xf1er:<EMAIL>:10005"
  DATABASE_URL: "**************************************************************************************************************************"
  # Production configuration
  PRODUCTION_DOMAIN: "propbolt.com"
  NODE_ENV: "production"
  # RealEstateAPI.com Integration
  REAL_ESTATE_API_KEY: "AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914"
  REAL_ESTATE_API_URL: "https://api.realestateapi.com/v2"
  # Google Cloud Platform
  GCP_PROJECT_ID: "gold-braid-458901-v2"
  GCP_PROJECT_NUMBER: "************"

# Automatic scaling configuration
automatic_scaling:
  min_instances: 1
  max_instances: 10
  target_cpu_utilization: 0.6

# Resource allocation
resources:
  cpu: 1
  memory_gb: 0.5

# Custom domain and routing configuration
handlers:
- url: /.*
  script: auto
  secure: always
