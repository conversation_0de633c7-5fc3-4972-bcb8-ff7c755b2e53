package realestate

import (
	"fmt"
)

// SkipTraceRequest represents the request for skip trace information
type SkipTraceRequest struct {
	Address    string `json:"address,omitempty"`
	PropertyID string `json:"propertyId,omitempty"`
	OwnerName  string `json:"ownerName,omitempty"`
	
	// Address parts (alternative to full address)
	House  string `json:"house,omitempty"`
	Street string `json:"street,omitempty"`
	City   string `json:"city,omitempty"`
	State  string `json:"state,omitempty"`
	Zip    string `json:"zip,omitempty"`
}

// SkipTraceBulkRequest represents the request for bulk skip trace
type SkipTraceBulkRequest struct {
	Properties []SkipTraceProperty `json:"properties"`
}

// SkipTraceProperty represents a single property for bulk skip trace
type SkipTraceProperty struct {
	ID         string `json:"id,omitempty"`
	Address    string `json:"address,omitempty"`
	PropertyID string `json:"propertyId,omitempty"`
	OwnerName  string `json:"ownerName,omitempty"`
}

// ContactInfo represents contact information for a person
type ContactInfo struct {
	Type  string `json:"type"` // phone, email, address
	Value string `json:"value"`
	Valid bool   `json:"valid"`
}

// Demographics represents demographic information
type DemographicInfo struct {
	Age           int    `json:"age,omitempty"`
	Gender        string `json:"gender,omitempty"`
	MaritalStatus string `json:"maritalStatus,omitempty"`
	Income        int    `json:"income,omitempty"`
	Education     string `json:"education,omitempty"`
	Occupation    string `json:"occupation,omitempty"`
}

// SocialProfile represents social media profile information
type SocialProfile struct {
	Platform string `json:"platform"`
	Username string `json:"username"`
	URL      string `json:"url"`
}

// Associate represents an associated person
type Associate struct {
	Name         string `json:"name"`
	Relationship string `json:"relationship"`
	Address      string `json:"address,omitempty"`
}

// SkipTraceResult represents the result of a skip trace search
type SkipTraceResult struct {
	PropertyID   string            `json:"propertyId,omitempty"`
	Address      string            `json:"address"`
	OwnerName    string            `json:"ownerName"`
	
	// Contact information
	Phones       []ContactInfo     `json:"phones,omitempty"`
	Emails       []ContactInfo     `json:"emails,omitempty"`
	Addresses    []ContactInfo     `json:"addresses,omitempty"`
	
	// Demographics
	Demographics *DemographicInfo  `json:"demographics,omitempty"`
	
	// Social profiles
	SocialProfiles []SocialProfile `json:"socialProfiles,omitempty"`
	
	// Associates
	Associates   []Associate       `json:"associates,omitempty"`
	
	// Additional information
	DeathRecord  bool              `json:"deathRecord,omitempty"`
	Bankruptcy   bool              `json:"bankruptcy,omitempty"`
	Foreclosure  bool              `json:"foreclosure,omitempty"`
	
	// Confidence score
	ConfidenceScore float64        `json:"confidenceScore,omitempty"`
	
	// Data sources
	Sources      []string          `json:"sources,omitempty"`
}

// SkipTraceResponse represents the response from skip trace API
type SkipTraceResponse struct {
	Results []SkipTraceResult `json:"results"`
	Count   int               `json:"count"`
}

// SkipTrace performs skip trace search for owner contact information
func (c *Client) SkipTrace(request *SkipTraceRequest) (*SkipTraceResponse, error) {
	if request.Address == "" && request.PropertyID == "" && request.House == "" {
		return nil, fmt.Errorf("address, property ID, or address parts are required")
	}

	resp, err := c.MakeRequest("/v1/SkipTrace", request)
	if err != nil {
		return nil, err
	}

	var result SkipTraceResponse
	if err := c.ParseResponse(resp, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// SkipTraceBulk performs bulk skip trace search for multiple properties
func (c *Client) SkipTraceBulk(request *SkipTraceBulkRequest) (*SkipTraceResponse, error) {
	if len(request.Properties) == 0 {
		return nil, fmt.Errorf("at least one property is required for bulk skip trace")
	}

	if len(request.Properties) > 1000 {
		return nil, fmt.Errorf("bulk skip trace is limited to 1000 properties per request")
	}

	resp, err := c.MakeRequest("/v1/SkipTraceBulk", request)
	if err != nil {
		return nil, err
	}

	var result SkipTraceResponse
	if err := c.ParseResponse(resp, &result); err != nil {
		return nil, err
	}

	return &result, nil
}
