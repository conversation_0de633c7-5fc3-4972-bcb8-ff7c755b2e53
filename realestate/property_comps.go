package realestate

import (
	"fmt"
)

// PropertyCompsRequest represents the request for property comparables
type PropertyCompsRequest struct {
	// Subject property (required)
	Subject *SubjectProperty `json:"subject"`
	
	// Search parameters
	Radius       float64 `json:"radius,omitempty"`       // Default 2 miles
	SaleRecency  int     `json:"saleRecency,omitempty"`  // Days, default 180
	CompCount    int     `json:"compCount,omitempty"`    // Number of comps, default 10
	
	// Boosts for better matching (v3 only)
	Boosts *CompsBoosts `json:"boosts,omitempty"`
	
	// Filters
	MinSimilarity   float64 `json:"minSimilarity,omitempty"`   // Default 0.7
	ExcludeSubject  bool    `json:"excludeSubject,omitempty"`  // Default true
	
	// Property type filters
	PropertyTypes []string `json:"propertyTypes,omitempty"`
	
	// Size filters
	MinSquareFeet int `json:"minSquareFeet,omitempty"`
	MaxSquareFeet int `json:"maxSquareFeet,omitempty"`
	
	// Age filters
	MinYearBuilt int `json:"minYearBuilt,omitempty"`
	MaxYearBuilt int `json:"maxYearBuilt,omitempty"`
}

// SubjectProperty represents the subject property for comparison
type SubjectProperty struct {
	ID               string  `json:"id,omitempty"`
	Address          string  `json:"address,omitempty"`
	Latitude         float64 `json:"latitude,omitempty"`
	Longitude        float64 `json:"longitude,omitempty"`
	Bedrooms         int     `json:"bedrooms,omitempty"`
	Bathrooms        float64 `json:"bathrooms,omitempty"`
	LivingSquareFeet int     `json:"livingSquareFeet,omitempty"`
	YearBuilt        int     `json:"yearBuilt,omitempty"`
	PropertyType     string  `json:"propertyType,omitempty"`
	LotSize          int     `json:"lotSize,omitempty"`
}

// CompsBoosts represents boost factors for comp matching (v3 API)
type CompsBoosts struct {
	SaleRecency float64 `json:"saleRecency,omitempty"` // Default 2.0
	Proximity   float64 `json:"proximity,omitempty"`   // Default 1.5
	Similarity  float64 `json:"similarity,omitempty"`  // Default 1.5
}

// ComparableProperty represents a single comparable property
type ComparableProperty struct {
	ID               string  `json:"id"`
	PropertyID       string  `json:"propertyId"`
	Address          string  `json:"address"`
	City             string  `json:"city"`
	State            string  `json:"state"`
	ZipCode          string  `json:"zipCode"`
	Latitude         float64 `json:"latitude"`
	Longitude        float64 `json:"longitude"`
	
	// Property details
	Bedrooms         int     `json:"bedrooms"`
	Bathrooms        float64 `json:"bathrooms"`
	LivingSquareFeet int     `json:"livingSquareFeet"`
	LotSize          int     `json:"lotSize"`
	YearBuilt        int     `json:"yearBuilt"`
	PropertyType     string  `json:"propertyType"`
	
	// Sale information
	SaleDate         string  `json:"saleDate"`
	SalePrice        int     `json:"salePrice"`
	PricePerSqFt     float64 `json:"pricePerSqFt"`
	
	// Distance and similarity
	DistanceMiles    float64 `json:"distanceMiles"`
	SimilarityScore  float64 `json:"similarityScore"`
	
	// Adjustments (v3 API)
	Adjustments      *CompAdjustments `json:"adjustments,omitempty"`
	AdjustedValue    int              `json:"adjustedValue,omitempty"`
}

// CompAdjustments represents adjustments made to comparable properties
type CompAdjustments struct {
	SizeAdjustment     int `json:"sizeAdjustment,omitempty"`
	AgeAdjustment      int `json:"ageAdjustment,omitempty"`
	LocationAdjustment int `json:"locationAdjustment,omitempty"`
	ConditionAdjustment int `json:"conditionAdjustment,omitempty"`
	TotalAdjustment    int `json:"totalAdjustment"`
}

// ValuationRange represents the estimated value range
type ValuationRange struct {
	Low    int `json:"low"`
	High   int `json:"high"`
	Mean   int `json:"mean"`
	Median int `json:"median"`
}

// PropertyCompsResponse represents the response from property comps API
type PropertyCompsResponse struct {
	Subject         *SubjectProperty      `json:"subject"`
	Comparables     []ComparableProperty  `json:"comparables"`
	ValuationRange  *ValuationRange       `json:"valuationRange,omitempty"`
	CompCount       int                   `json:"compCount"`
	SearchRadius    float64               `json:"searchRadius"`
	SaleRecency     int                   `json:"saleRecency"`
	ExecutionTimeMS int                   `json:"executionTimeMS,omitempty"`
}

// PropertyComps fetches comparable properties (v2 API)
func (c *Client) PropertyComps(request *PropertyCompsRequest) (*PropertyCompsResponse, error) {
	if request.Subject == nil {
		return nil, fmt.Errorf("subject property is required")
	}

	resp, err := c.MakeRequest("/v2/PropertyComps", request)
	if err != nil {
		return nil, err
	}

	var result PropertyCompsResponse
	if err := c.ParseResponse(resp, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// PropertyCompsV3 fetches comparable properties with advanced features (v3 API)
func (c *Client) PropertyCompsV3(request *PropertyCompsRequest) (*PropertyCompsResponse, error) {
	if request.Subject == nil {
		return nil, fmt.Errorf("subject property is required")
	}

	// Set default boosts for v3 API
	if request.Boosts == nil {
		request.Boosts = &CompsBoosts{
			SaleRecency: 2.0,
			Proximity:   1.5,
			Similarity:  1.5,
		}
	}

	resp, err := c.MakeRequest("/v3/PropertyComps", request)
	if err != nil {
		return nil, err
	}

	var result PropertyCompsResponse
	if err := c.ParseResponse(resp, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// PropertyCompsV2 fetches comparable properties using generic interface (v2 API)
func (c *Client) PropertyCompsV2(request map[string]interface{}) (map[string]interface{}, error) {
	resp, err := c.MakeRequest("/v2/PropertyComps", request)
	if err != nil {
		return nil, err
	}

	var result map[string]interface{}
	if err := c.ParseResponse(resp, &result); err != nil {
		return nil, err
	}

	return result, nil
}
