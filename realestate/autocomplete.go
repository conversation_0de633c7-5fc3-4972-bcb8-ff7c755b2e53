package realestate

import (
	"fmt"
)

// AutoCompleteRequest represents the request for address autocomplete
type AutoCompleteRequest struct {
	Search      string   `json:"search"`
	SearchTypes []string `json:"search_types,omitempty"`
}

// AutoCompleteResult represents a single autocomplete result
type AutoCompleteResult struct {
	Title      string `json:"title"`
	City       string `json:"city"`
	State      string `json:"state"`
	StateCode  string `json:"state_code"`
	ZipCode    string `json:"zipCode"`
	Zip        string `json:"zip"`
	Address    string `json:"address"`
	SearchType string `json:"searchType"`
}

// AutoCompleteResponse represents the response from autocomplete API
type AutoCompleteResponse struct {
	Results []AutoCompleteResult `json:"results"`
	Data    []AutoCompleteResult `json:"data"`
}

// AutoComplete performs address autocomplete search
func (c *Client) AutoComplete(search string, searchTypes []string) (*AutoCompleteResponse, error) {
	if len(search) < 3 {
		return nil, fmt.Errorf("search query must be at least 3 characters long")
	}

	if searchTypes == nil {
		searchTypes = []string{"A", "C"} // Default to address and city search
	}

	request := AutoCompleteRequest{
		Search:      search,
		SearchTypes: searchTypes,
	}

	resp, err := c.MakeRequest("/v2/AutoComplete", request)
	if err != nil {
		return nil, err
	}

	var result AutoCompleteResponse
	if err := c.ParseResponse(resp, &result); err != nil {
		return nil, err
	}

	// Normalize results to ensure consistent structure
	if result.Results == nil && result.Data != nil {
		result.Results = result.Data
	}

	// Process and normalize the results
	for i := range result.Results {
		item := &result.Results[i]
		
		// Ensure consistent field mapping
		if item.City == "" && item.Title != "" {
			// Try to extract city from title if needed
		}
		if item.State == "" && item.StateCode != "" {
			item.State = item.StateCode
		}
		if item.ZipCode == "" && item.Zip != "" {
			item.ZipCode = item.Zip
		}
		if item.Title == "" {
			if item.Address != "" {
				item.Title = item.Address
			} else if item.City != "" && item.State != "" {
				item.Title = fmt.Sprintf("%s, %s", item.City, item.State)
			}
		}
		if item.SearchType == "" {
			item.SearchType = "C" // Default to city
		}
	}

	return &result, nil
}
