package realestate

import (
	"fmt"
)

// PropertyDetailRequest represents the request for property details
type PropertyDetailRequest struct {
	ID     string `json:"id,omitempty"`
	Address string `json:"address,omitempty"`
	House  string `json:"house,omitempty"`
	Street string `json:"street,omitempty"`
	City   string `json:"city,omitempty"`
	State  string `json:"state,omitempty"`
	Zip    string `json:"zip,omitempty"`
}

// PropertyInfo represents basic property information
type PropertyInfo struct {
	Bedrooms         int     `json:"bedrooms"`
	Bathrooms        float64 `json:"bathrooms"`
	LivingSquareFeet int     `json:"livingSquareFeet"`
	YearBuilt        int     `json:"yearBuilt"`
	PropertyType     string  `json:"propertyType"`
	Stories          int     `json:"stories"`
	Units            int     `json:"units"`
}

// AddressInfo represents property address information
type AddressInfo struct {
	FullAddress string `json:"fullAddress"`
	House       string `json:"house"`
	Street      string `json:"street"`
	City        string `json:"city"`
	State       string `json:"state"`
	ZipCode     string `json:"zipCode"`
	County      string `json:"county"`
}

// LocationInfo represents property location coordinates
type LocationInfo struct {
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
}

// OwnerInfo represents property owner information
type OwnerInfo struct {
	Name         string `json:"name"`
	MailingAddress string `json:"mailingAddress"`
	AbsenteeOwner bool   `json:"absenteeOwner"`
	CorporateOwned bool  `json:"corporateOwned"`
}

// TaxInfo represents property tax information
type TaxInfo struct {
	TaxYear        int     `json:"taxYear"`
	TaxAmount      float64 `json:"taxAmount"`
	AssessedValue  int     `json:"assessedValue"`
	LandValue      int     `json:"landValue"`
	ImprovementValue int   `json:"improvementValue"`
}

// SaleHistory represents property sale history
type SaleHistory struct {
	SaleDate   string  `json:"saleDate"`
	SalePrice  int     `json:"salePrice"`
	SaleType   string  `json:"saleType"`
	Buyer      string  `json:"buyer"`
	Seller     string  `json:"seller"`
}

// MLSInfo represents MLS listing information
type MLSInfo struct {
	Active        bool   `json:"active"`
	ListingPrice  int    `json:"listingPrice"`
	DaysOnMarket  int    `json:"daysOnMarket"`
	Status        string `json:"status"`
	ListingDate   string `json:"listingDate"`
	MLSNumber     string `json:"mlsNumber"`
}

// Demographics represents neighborhood demographics
type Demographics struct {
	MedianIncome     int     `json:"medianIncome"`
	MedianHomeValue  int     `json:"medianHomeValue"`
	Population       int     `json:"population"`
	RentalEstimates  *RentalEstimates `json:"rentalEstimates,omitempty"`
}

// RentalEstimates represents rental value estimates
type RentalEstimates struct {
	RentEstimate int `json:"rentEstimate"`
	RentLow      int `json:"rentLow"`
	RentHigh     int `json:"rentHigh"`
}

// PropertyDetailResponse represents the comprehensive property detail response
type PropertyDetailResponse struct {
	ID                string           `json:"id"`
	PropertyID        string           `json:"propertyId"`
	Address           *AddressInfo     `json:"address"`
	PropertyInfo      *PropertyInfo    `json:"propertyInfo"`
	Location          *LocationInfo    `json:"location"`
	OwnerInfo         *OwnerInfo       `json:"ownerInfo"`
	TaxInfo           *TaxInfo         `json:"taxInfo"`
	EstimatedValue    int              `json:"estimatedValue"`
	EstimatedRent     int              `json:"estimatedRent"`
	SuggestedRent     int              `json:"suggestedRent"`
	SaleHistory       []SaleHistory    `json:"saleHistory"`
	LastSaleDate      string           `json:"lastSaleDate"`
	LastSaleAmount    int              `json:"lastSaleAmount"`
	MLSInfo           *MLSInfo         `json:"mlsInfo"`
	Demographics      *Demographics    `json:"demographics"`
	AbsenteeOwner     bool             `json:"absenteeOwner"`
	CorporateOwned    bool             `json:"corporateOwned"`
	Vacant            bool             `json:"vacant"`
	Distressed        bool             `json:"distressed"`
	Foreclosure       bool             `json:"foreclosure"`
	PreForeclosure    bool             `json:"preForeclosure"`
	HighEquity        bool             `json:"highEquity"`
}

// PropertyDetail fetches comprehensive property information
func (c *Client) PropertyDetail(request *PropertyDetailRequest) (*PropertyDetailResponse, error) {
	// Validate request parameters
	if request.ID == "" && request.Address == "" && request.House == "" {
		return nil, fmt.Errorf("request must include either a property ID, full address, or address parts")
	}

	// Validate address parts if using that method
	if request.House != "" {
		if len(request.State) != 2 {
			return nil, fmt.Errorf("state must be a 2-character abbreviation")
		}
		if len(request.Zip) != 5 {
			return nil, fmt.Errorf("zip code must be 5 characters long")
		}
	}

	resp, err := c.MakeRequest("/v2/PropertyDetail", request)
	if err != nil {
		return nil, err
	}

	var result struct {
		Data *PropertyDetailResponse `json:"data"`
		*PropertyDetailResponse
	}

	if err := c.ParseResponse(resp, &result); err != nil {
		return nil, err
	}

	// Handle different response structures
	if result.Data != nil {
		return result.Data, nil
	}
	if result.PropertyDetailResponse != nil {
		return result.PropertyDetailResponse, nil
	}

	return nil, fmt.Errorf("unexpected response structure")
}

// PropertyDetailBulkRequest represents a bulk property detail request
type PropertyDetailBulkRequest struct {
	Properties []PropertyDetailRequest `json:"properties"`
}

// PropertyDetailBulkResponse represents a bulk property detail response
type PropertyDetailBulkResponse struct {
	Results []PropertyDetailResponse `json:"results"`
	Count   int                      `json:"count"`
}

// PropertyDetailBulk fetches comprehensive property information for multiple properties
func (c *Client) PropertyDetailBulk(request map[string]interface{}) (map[string]interface{}, error) {
	resp, err := c.MakeRequest("/v2/PropertyDetailBulk", request)
	if err != nil {
		return nil, err
	}

	var result map[string]interface{}
	if err := c.ParseResponse(resp, &result); err != nil {
		return nil, err
	}

	return result, nil
}
