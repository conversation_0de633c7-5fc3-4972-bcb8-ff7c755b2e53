package realestate

import (
	"fmt"
)

// PropertySearchRequest represents the comprehensive property search request
type PropertySearchRequest struct {
	// Location parameters
	City      string  `json:"city,omitempty"`
	State     string  `json:"state,omitempty"`
	Zip       string  `json:"zip,omitempty"`
	Address   string  `json:"address,omitempty"`
	County    string  `json:"county,omitempty"`
	Latitude  float64 `json:"latitude,omitempty"`
	Longitude float64 `json:"longitude,omitempty"`
	Radius    float64 `json:"radius,omitempty"`

	// Property type
	PropertyType string `json:"property_type,omitempty"`

	// Property details
	BedsMin         int `json:"beds_min,omitempty"`
	BedsMax         int `json:"beds_max,omitempty"`
	BathsMin        int `json:"baths_min,omitempty"`
	BathsMax        int `json:"baths_max,omitempty"`
	BuildingSizeMin int `json:"building_size_min,omitempty"`
	BuildingSizeMax int `json:"building_size_max,omitempty"`
	LotSizeMin      int `json:"lot_size_min,omitempty"`
	LotSizeMax      int `json:"lot_size_max,omitempty"`
	YearBuiltMin    int `json:"year_built_min,omitempty"`
	YearBuiltMax    int `json:"year_built_max,omitempty"`
	UnitsMin        int `json:"units_min,omitempty"`
	UnitsMax        int `json:"units_max,omitempty"`

	// Financial filters
	ValueMin      int    `json:"value_min,omitempty"`
	ValueMax      int    `json:"value_max,omitempty"`
	EquityMin     int    `json:"equity_min,omitempty"`
	EquityMax     int    `json:"equity_max,omitempty"`
	SalePriceMin  int    `json:"sale_price_min,omitempty"`
	SalePriceMax  int    `json:"sale_price_max,omitempty"`
	SaleDateMin   string `json:"sale_date_min,omitempty"`
	SaleDateMax   string `json:"sale_date_max,omitempty"`

	// MLS parameters
	MLSActive             bool `json:"mls_active,omitempty"`
	MLSPending            bool `json:"mls_pending,omitempty"`
	MLSSold               bool `json:"mls_sold,omitempty"`
	MLSDaysOnMarketMin    int  `json:"mls_days_on_market_min,omitempty"`
	MLSDaysOnMarketMax    int  `json:"mls_days_on_market_max,omitempty"`

	// Ownership filters
	AbsenteeOwner        bool `json:"absentee_owner,omitempty"`
	CorporateOwned       bool `json:"corporate_owned,omitempty"`
	TrustOwned           bool `json:"trust_owned,omitempty"`
	LLCOwned             bool `json:"llc_owned,omitempty"`
	OwnerOccupied        bool `json:"owner_occupied,omitempty"`
	OwnershipLengthMin   int  `json:"ownership_length_min,omitempty"`
	OwnershipLengthMax   int  `json:"ownership_length_max,omitempty"`

	// Property condition
	Vacant         bool   `json:"vacant,omitempty"`
	Distressed     bool   `json:"distressed,omitempty"`
	Foreclosure    bool   `json:"foreclosure,omitempty"`
	PreForeclosure bool   `json:"pre_foreclosure,omitempty"`
	Auction        bool   `json:"auction,omitempty"`
	TaxLien        bool   `json:"tax_lien,omitempty"`
	CodeViolation  bool   `json:"code_violation,omitempty"`
	FloodZone      string `json:"flood_zone,omitempty"`

	// Investment filters
	HighEquity      bool `json:"high_equity,omitempty"`
	NegativeEquity  bool `json:"negative_equity,omitempty"`
	FreeClear       bool `json:"free_clear,omitempty"`
	CashBuyer       bool `json:"cash_buyer,omitempty"`
	AssumableLoan   bool `json:"assumable_loan,omitempty"`
	REO             bool `json:"reo,omitempty"`
	QuitClaim       bool `json:"quit_claim,omitempty"`
	FlippedMinTimes int  `json:"flipped_min_times,omitempty"`
	FlippedWithinYears int `json:"flipped_within_years,omitempty"`

	// Construction/features
	ConstructionType string `json:"construction_type,omitempty"`
	HeatingType      string `json:"heating_type,omitempty"`
	CoolingType      string `json:"cooling_type,omitempty"`
	Pool             bool   `json:"pool,omitempty"`
	Garage           bool   `json:"garage,omitempty"`
	Basement         bool   `json:"basement,omitempty"`
	Waterfront       bool   `json:"waterfront,omitempty"`

	// Multi-family
	MFH2to4 bool `json:"mfh_2to4,omitempty"`

	// Special modes
	Count    bool     `json:"count,omitempty"`
	Summary  bool     `json:"summary,omitempty"`
	IDsOnly  bool     `json:"ids_only,omitempty"`
	Exclude  []string `json:"exclude,omitempty"`

	// Pagination and sorting
	Sort        map[string]string `json:"sort,omitempty"`
	ResultIndex int               `json:"resultIndex,omitempty"`
	Size        int               `json:"size,omitempty"`
	UserID      string            `json:"userId,omitempty"`
}

// PropertySearchResult represents a single property in search results
type PropertySearchResult struct {
	ID           string  `json:"id"`
	PropertyID   string  `json:"propertyId"`
	Address      string  `json:"address"`
	City         string  `json:"city"`
	State        string  `json:"state"`
	ZipCode      string  `json:"zipCode"`
	Latitude     float64 `json:"latitude"`
	Longitude    float64 `json:"longitude"`
	PropertyType string  `json:"propertyType"`
	Bedrooms     int     `json:"bedrooms"`
	Bathrooms    float64 `json:"bathrooms"`
	SquareFeet   int     `json:"squareFeet"`
	LotSize      int     `json:"lotSize"`
	YearBuilt    int     `json:"yearBuilt"`
	Value        int     `json:"value"`
	Equity       int     `json:"equity"`
	MLSActive    bool    `json:"mlsActive"`
	MLSPrice     int     `json:"mlsPrice"`
	Vacant       bool    `json:"vacant"`
	Distressed   bool    `json:"distressed"`
}

// PropertySearchResponse represents the response from property search API
type PropertySearchResponse struct {
	Results     []PropertySearchResult `json:"results"`
	ResultCount int                    `json:"resultCount"`
	Pagination  *PaginationInfo        `json:"pagination,omitempty"`
}

// PaginationInfo represents pagination information
type PaginationInfo struct {
	CurrentPage  int  `json:"currentPage"`
	TotalPages   int  `json:"totalPages"`
	PageSize     int  `json:"pageSize"`
	TotalResults int  `json:"totalResults"`
	HasMore      bool `json:"hasMore"`
}

// PropertySearch performs a comprehensive property search
func (c *Client) PropertySearch(request *PropertySearchRequest) (*PropertySearchResponse, error) {
	// Validate that at least one search criteria is provided
	hasGeoParam := request.City != "" || request.State != "" || request.Zip != "" || 
		request.County != "" || (request.Latitude != 0 && request.Longitude != 0)
	
	if !hasGeoParam && request.PropertyType == "" && !request.MLSActive {
		return nil, fmt.Errorf("at least one search criteria must be provided (location or property filter)")
	}

	// Set defaults
	if request.Size == 0 {
		request.Size = 25
	}
	if request.Latitude != 0 && request.Longitude != 0 && request.Radius == 0 {
		request.Radius = 10 // Default 10 mile radius for coordinate searches
	}

	resp, err := c.MakeRequest("/v2/PropertySearch", request)
	if err != nil {
		return nil, err
	}

	var result PropertySearchResponse
	if err := c.ParseResponse(resp, &result); err != nil {
		return nil, err
	}

	// Add pagination info if results exist
	if result.ResultCount > 0 {
		resultIndex := request.ResultIndex
		size := request.Size
		if size == 0 {
			size = 25
		}

		result.Pagination = &PaginationInfo{
			CurrentPage:  (resultIndex / size) + 1,
			TotalPages:   (result.ResultCount + size - 1) / size,
			PageSize:     size,
			TotalResults: result.ResultCount,
			HasMore:      resultIndex+size < result.ResultCount,
		}
	}

	return &result, nil
}
