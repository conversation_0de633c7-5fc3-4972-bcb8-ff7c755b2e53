package realestate

import (
	"fmt"
)

// PropertyMappingRequest represents the request for property mapping (pins)
type PropertyMappingRequest struct {
	IDs    []string                 `json:"ids,omitempty"`
	Search *PropertyMappingSearch   `json:"search,omitempty"`
}

// PropertyMappingSearch represents search parameters for mapping
type PropertyMappingSearch struct {
	// Geographic bounds
	NorthEastLat float64 `json:"northEastLat,omitempty"`
	NorthEastLng float64 `json:"northEastLng,omitempty"`
	SouthWestLat float64 `json:"southWestLat,omitempty"`
	SouthWestLng float64 `json:"southWestLng,omitempty"`
	
	// Center point with radius
	Latitude  float64 `json:"latitude,omitempty"`
	Longitude float64 `json:"longitude,omitempty"`
	Radius    float64 `json:"radius,omitempty"`
	
	// Property filters
	PropertyType   string `json:"property_type,omitempty"`
	MLSActive      bool   `json:"mls_active,omitempty"`
	Vacant         bool   `json:"vacant,omitempty"`
	Distressed     bool   `json:"distressed,omitempty"`
	AbsenteeOwner  bool   `json:"absentee_owner,omitempty"`
	CorporateOwned bool   `json:"corporate_owned,omitempty"`
	
	// Value filters
	ValueMin int `json:"value_min,omitempty"`
	ValueMax int `json:"value_max,omitempty"`
	
	// Limit results
	Limit int `json:"limit,omitempty"`
}

// PropertyPin represents a single property pin for mapping
type PropertyPin struct {
	ID           string  `json:"id"`
	PropertyID   string  `json:"propertyId"`
	Latitude     float64 `json:"latitude"`
	Longitude    float64 `json:"longitude"`
	Address      string  `json:"address"`
	City         string  `json:"city"`
	State        string  `json:"state"`
	ZipCode      string  `json:"zipCode"`
	PropertyType string  `json:"propertyType"`
	Value        int     `json:"value"`
	MLSActive    bool    `json:"mlsActive"`
	MLSPrice     int     `json:"mlsPrice,omitempty"`
	Vacant       bool    `json:"vacant"`
	Distressed   bool    `json:"distressed"`
	Bedrooms     int     `json:"bedrooms,omitempty"`
	Bathrooms    float64 `json:"bathrooms,omitempty"`
	SquareFeet   int     `json:"squareFeet,omitempty"`
	YearBuilt    int     `json:"yearBuilt,omitempty"`
}

// PropertyMappingResponse represents the response from property mapping API
type PropertyMappingResponse struct {
	Pins  []PropertyPin `json:"pins"`
	Count int           `json:"count"`
	Bounds *MapBounds   `json:"bounds,omitempty"`
}

// MapBounds represents the geographic bounds of the results
type MapBounds struct {
	NorthEast *Coordinate `json:"northEast"`
	SouthWest *Coordinate `json:"southWest"`
}

// Coordinate represents a geographic coordinate
type Coordinate struct {
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
}

// PropertyMapping fetches property pins for mapping display
func (c *Client) PropertyMapping(request *PropertyMappingRequest) (*PropertyMappingResponse, error) {
	if request.IDs == nil && request.Search == nil {
		return nil, fmt.Errorf("either property IDs or search parameters are required")
	}

	resp, err := c.MakeRequest("/v2/PropertyMapping", request)
	if err != nil {
		return nil, err
	}

	var result PropertyMappingResponse
	if err := c.ParseResponse(resp, &result); err != nil {
		return nil, err
	}

	return &result, nil
}
