PropBolt Brain Enterprise is now deployed with full Google Cloud CLI automation, enterprise auto-scaling, global load balancing, high-availability PostgreSQL, and comprehensive Google Cloud Marketplace applications.**


# PropBolt Users


# PropBolt Brain - Admin Vacant Land Analysis Platform
A production-grade, admin-only platform for advanced vacant land search, analysis, and user management. Built with Go backend and integrates with RealEstateAPI.com for comprehensive property data.

# PropBolt Brain - Enterprise Deployment Guide

## 🏢 Enterprise Google Cloud Platform Deployment

This guide confirms the enterprise-grade deployment of PropBolt Brain using Google Cloud CLI with auto-scaling, load balancing, high-availability PostgreSQL, and Google Cloud Marketplace applications.

---

## ✅ **CONFIRMED: Google Cloud CLI Usage**

**All infrastructure is deployed using `gcloud` CLI commands:**
- ✅ Cloud Run with enterprise auto-scaling
- ✅ Global Load Balancer with CDN
- ✅ High-availability Cloud SQL PostgreSQL
- ✅ Google Cloud Marketplace applications
- ✅ Enterprise monitoring and security

---

## 🚀 **Auto-Scaling Configuration**

### **Cloud Run Auto-Scaling**
```bash
# Enterprise auto-scaling configuration
--min-instances 2              # Always-on instances
--max-instances 100            # Scale up to 100 instances
--concurrency 1000             # 1000 concurrent requests per instance
--cpu 2                        # 2 vCPU per instance
--memory 2Gi                   # 2GB RAM per instance
--cpu-boost                    # CPU boost for cold starts
--execution-environment gen2   # Latest generation runtime
```

### **Auto-Scaling Policies**
- **Minimum Instances**: 2 (always running)
- **Maximum Instances**: 100 (enterprise scale)
- **Concurrency**: 1000 requests per instance
- **CPU Throttling**: Enabled for cost optimization
- **Session Affinity**: Configured for stateful operations

---

## ⚖️ **Load Balancing Architecture**

### **Global Load Balancer**
```bash
# Global HTTPS load balancer with CDN
gcloud compute backend-services create brain-backend-service --global
gcloud compute url-maps create brain-url-map
gcloud compute target-https-proxies create brain-https-proxy
gcloud compute forwarding-rules create brain-forwarding-rule --global
```

### **Load Balancing Features**
- ✅ **Global Distribution**: Traffic routed to nearest region
- ✅ **HTTPS Termination**: SSL/TLS handled at load balancer
- ✅ **CDN Integration**: Static assets cached globally
- ✅ **Health Checks**: Automatic unhealthy instance removal
- ✅ **Session Affinity**: Sticky sessions when needed

---

## 🗄️ **Enterprise PostgreSQL Configuration**

### **High-Availability Setup**


### **Database Architecture**
- **Primary Instance**: (Regional )
- **Read Replica 1**: `us-east1` (Same )
- **Read Replica 2**: `us-west1` (Cross-region)
- **Backup Strategy**: 30-day retention + Point-in-time recovery
- **Monitoring**: Database insights and query performance tracking

### **Database Features**
- ✅ **Regional High Availability**: Automatic failover
- ✅ **Read Replicas**: Load distribution across regions
- ✅ **Automated Backups**: 30-day retention
- ✅ **Point-in-Time Recovery**: Restore to any second
- ✅ **Performance Insights**: Query optimization
- ✅ **Connection Pooling**: Efficient connection management

---

## 🏪 **Google Cloud Marketplace Applications**





**Features:**
- Real-time application monitoring
- Custom dashboards and alerts
- Log aggregation and analysis
- Distributed tracing
- Performance metrics



**Features:**
- Advanced visualization dashboards
- Enterprise authentication
- Team collaboration features
- Custom alerting rules
- Data source integrations

#### **3. Redis Enterprise**
```yaml
# Redis Enterprise cluster
resources:
- name: redis-enterprise-cluster
  type: gcp-marketplace/redislabs-public:redis-enterprise
  properties:
    clusterSize: 3
    enableSSL: true
    enableAuth: true
```

**Features:**
- High-availability caching
- Multi-node clustering
- SSL/TLS encryption
- Authentication and authorization
- Advanced data structures

---

## 📊 **Enterprise Monitoring Stack**

### **Google Cloud Native Monitoring**
- **Cloud Monitoring**: Metrics, alerts, and dashboards
- **Cloud Logging**: Centralized log management
- **Cloud Trace**: Distributed request tracing
- **Cloud Profiler**: Application performance profiling
- **Error Reporting**: Automatic error detection and grouping

### **Third-Party Monitoring**
- **LogRocket**: User session recording and debugging

### **Key Metrics Tracked**
- API response times and latency
- Error rates and types
- Request volume and patterns
- Database performance
- Infrastructure utilization
- User experience metrics

---

## 🔒 **Enterprise Security Features**

### **Network Security**
```bash
# VPC and firewall configuration
gcloud compute networks create brain-vpc --subnet-mode=custom
gcloud compute firewall-rules create brain-allow-https
gcloud compute security-policies create brain-security-policy
```

### **Security Components**
- ✅ **Cloud Armor**: DDoS protection and WAF
- ✅ **VPC**: Isolated network environment
- ✅ **Firewall Rules**: Restricted access controls
- ✅ **Security Command Center**: Threat detection
- ✅ **Rate Limiting**: 100 requests per minute per IP
- ✅ **SSL/TLS**: End-to-end encryption

### **Access Control**
- **Service Accounts**: Least privilege access
- **IAM Roles**: Granular permissions
- **API Keys**: Secure server-side storage
- **Authentication**: BetterAuth integration

---

## 🚀 **Performance Optimizations**

### **Caching Strategy**
- **CDN**: Global content delivery network
- **Redis**: In-memory data caching
- **Application-level**: Response caching
- **Database**: Query result caching

### **Scaling Features**
- **Horizontal Scaling**: 2-100 instances
- **Vertical Scaling**: CPU and memory optimization
- **Geographic Distribution**: Multi-region deployment
- **Connection Pooling**: Efficient database connections

---

## 📈 **Deployment Commands**



### **Manual Verification**
```bash
# Verify auto-scaling configuration
gcloud run services describe brain-api --region=us-east1

# Check load balancer status
gcloud compute backend-services list

# Verify database HA setup
gcloud sql instances list

# Check marketplace applications
gcloud deployment-manager deployments list
```

---

## 🎯 **Enterprise Compliance**

### **Confirmed Requirements**
- ✅ **Google Cloud CLI**: All infrastructure via gcloud
- ✅ **Auto-Scaling**: 2-100 instances with intelligent scaling
- ✅ **Load Balancing**: Global HTTPS load balancer with CDN
- ✅ **PostgreSQL**: High-availability with read replicas
- ✅ **Marketplace Apps**: Enterprise monitoring and caching
- ✅ **Security**: Enterprise-grade security controls
- ✅ **Monitoring**: Comprehensive observability stack



## 🏗️ Architecture Overview

PropBolt Brain consists of:
- **Legacy PropBolt API**: Existing property search and analysis functionality
- **RealEstateAPI.com Proxy Layer**: Secure server-side integration with external APIs
- **Admin Authentication**: BetterAuth integration for role-based access control
- **Google Cloud Infrastructure**: Production deployment on GCP

## 📋 Architecture Overview

### **Dual API Integration**
- **RealEstateAPI.com** - Professional real estate data (9 endpoints)
- **PropBolt Internal API** - Legacy Zillow integration with proxy rotation

## 🔧 Prerequisites

1. **Google Cloud CLI** installed and authenticated
2. **Node.js 20+** and **Go 1.22+**
3. **Project Access** to `gold-braid-458901-v2`
4. **Domain Configuration** for all PropBolt domains

## 🌐 Domain Configuration

### **Production Domains**
- `propbolt.com` → Frontend (Next.js)
- `brain.propbolt.com` → Admin API (Go)
- `api.propbolt.com` → User API (Go)
- `admin.propbolt.com` → Admin redirect (Go)
- `go.propbolt.com` → User redirect (Go)

- **[Complete API Documentation](BRAIN_API_DOCUMENTATION.md)**: Detailed endpoint documentation
- **[Technical Plan](BRAIN_PROPBOLT_TECHNICAL_PLAN.md)**: Comprehensive implementation plan
## 🔐 Authentication System

### **NextAuth.js Integration**
Login, Sign Up, Forgot Password

### **Account Types**
- `admin` - Access to land search dashboard and user management
- `user` - Access to data internal GoLang API
- `suspend` - Login prevented
- `null` - Login prevented

## 🚀 Features

### Core Functionality
- **Property Details**: Comprehensive property information by ID, URL, or address
- **Enhanced Search**: Advanced filtering with vacant land specific parameters
- **Mapping Integration**: Interactive maps with property pins and boundaries
- **Commercial Analysis**: Business compatibility scoring and ROI projections
- **Watch List Management**: Save and categorize properties of interest
- **Market Analytics**: Trend analysis and price predictions

### RealEstateAPI.com Integration
- **AutoComplete**: Address suggestions and location autocomplete
- **Property Mapping**: Map pin locations for visualization
- **Property Details**: Comprehensive property data including owner info
- **Bulk Operations**: Process up to 1000 properties simultaneously
- **Property Search**: Advanced search with complex filtering
- **Liens Information**: Involuntary liens and legal data
- **Comparables**: Property comps with v2 and v3 algorithms
- **SkipTrace**: Owner contact information and demographics

### Admin Features
- **Role-Based Access**: Admin-only authentication via propbolt.com/login
- **User Management**: Admin dashboard for user oversight
- **System Configuration**: API endpoint testing and monitoring
- **Analytics Dashboard**: Usage metrics and system health


## 🔒 Security

- **Admin-Only Access**: All endpoints require admin authentication
- **API Key Security**: RealEstateAPI.com key stored server-side only
- **CORS Configuration**: Restricted to authorized domains
- **Rate Limiting**: Implemented to prevent abuse
- **Input Validation**: All inputs validated and sanitized

1. **Frontend Development**: Build Next.js admin dashboard
2. **Database Integration**: Implement PostgreSQL schema
3. **Authentication**: Complete BetterAuth (replace NextAuth) integration
4. **Testing**: Comprehensive test suite
5. **Monitoring**: Advanced analytics and alerting

## **Phase 4: Backend API Development (GoLang)**

### **4.1 Proxy Layer Architecture**
Create secure proxy endpoints that map 1:1 with RealEstateAPI.com endpoints:

```go
// Internal proxy endpoints for brain.propbolt.com
/api/v1/proxy/autocomplete          -> RealEstateAPI AutoComplete
/api/v1/proxy/mapping               -> RealEstateAPI Mapping
/api/v1/proxy/property-detail       -> RealEstateAPI Property Detail
/api/v1/proxy/property-detail-bulk  -> RealEstateAPI Property Detail Bulk
/api/v1/proxy/property-search       -> RealEstateAPI Property Search
/api/v1/proxy/involuntary-liens     -> RealEstateAPI Involuntary Liens
/api/v1/proxy/property-comps-v3     -> RealEstateAPI PropertyComps v3
/api/v1/proxy/property-comps-v2     -> RealEstateAPI PropertyComps v2
/api/v1/proxy/skiptrace             -> RealEstateAPI SkipTrace
```

### **4.2 Enhanced Internal Endpoints**
```go
// Enhanced internal endpoints
GET  /api/v1/properties             // List properties with filters
POST /api/v1/properties/search      // Advanced search with caching
GET  /api/v1/properties/:id         // Get property details
POST /api/v1/properties/:id/save    // Save property to watchlist
GET  /api/v1/saved-properties       // Get user's saved properties
POST /api/v1/search/save            // Save search parameters
GET  /api/v1/search/history         // Get search history
GET  /api/v1/analytics/dashboard    // Dashboard analytics
POST /api/v1/notifications          // Create notifications
GET  /api/v1/notifications          // Get user notifications
```

Real Estate API does not need proxy. Only internal API.

## **Phase 4: Backend API Development (GoLang)**

### **4.1 Proxy Layer Architecture**
Create secure proxy endpoints that map 1:1 with RealEstateAPI.com endpoints:

```go
// Internal proxy endpoints for brain.propbolt.com
/api/v1/proxy/autocomplete          -> RealEstateAPI AutoComplete
/api/v1/proxy/mapping               -> RealEstateAPI Mapping
/api/v1/proxy/property-detail       -> RealEstateAPI Property Detail
/api/v1/proxy/property-detail-bulk  -> RealEstateAPI Property Detail Bulk
/api/v1/proxy/property-search       -> RealEstateAPI Property Search
/api/v1/proxy/involuntary-liens     -> RealEstateAPI Involuntary Liens
/api/v1/proxy/property-comps-v3     -> RealEstateAPI PropertyComps v3
/api/v1/proxy/property-comps-v2     -> RealEstateAPI PropertyComps v2
/api/v1/proxy/skiptrace             -> RealEstateAPI SkipTrace
```

### **4.2 Enhanced Internal Endpoints**
```go
// Enhanced internal endpoints
GET  /api/v1/properties             // List properties with filters
POST /api/v1/properties/search      // Advanced search with caching
GET  /api/v1/properties/:id         // Get property details
POST /api/v1/properties/:id/save    // Save property to watchlist
GET  /api/v1/saved-properties       // Get user's saved properties
POST /api/v1/search/save            // Save search parameters
GET  /api/v1/search/history         // Get search history
GET  /api/v1/analytics/dashboard    // Dashboard analytics
POST /api/v1/notifications          // Create notifications
GET  /api/v1/notifications          // Get user notifications
```

---


---

// Internal proxy endpoints for brain.propbolt.com - no proxy needed
/api/v1/proxy/autocomplete           ✅ LIVE
/api/v1/proxy/property-search        ✅ LIVE  
/api/v1/proxy/property-detail        ✅ LIVE
/api/v1/proxy/property-detail-bulk   ✅ LIVE
/api/v1/proxy/mapping                ✅ LIVE
/api/v1/proxy/property-comps-v3      ✅ LIVE
/api/v1/proxy/property-comps-v2      ✅ LIVE
/api/v1/proxy/involuntary-liens      ✅ LIVE
/api/v1/proxy/skiptrace              ✅ LIVE

// Internal PropBolt APIs - LIVE - Proxy Needed - Users Only api.propbolt.com
/property                            ✅ LIVE
/search                              ✅ LIVE
/search/sold                         ✅ LIVE
/search/rentals                      ✅ LIVE
/api/search                          ✅ LIVE
/api/dashboard/stats                 ✅ LIVE
/api/sync-properties                 ✅ LIVE
/api/refresh-data                    ✅ LIVE

// Authentication APIs - LIVE
/api/auth/user                       ✅ LIVE
/api/auth/create-user                ✅ LIVE

// MISSING BACKEND ENDPOINTS: please create. use api.propbolt.com as target
/api/v1/user/api-keys                ❌ NOT IMPLEMENTED
/api/v1/user/api-keys/create         ❌ NOT IMPLEMENTED  
/api/v1/user/api-keys/delete         ❌ NOT IMPLEMENTED
/api/v1/user/api-keys/usage          ❌ NOT IMPLEMENTED
/api/v1/user/api-keys/stats          ❌ NOT IMPLEMENTED

-- MISSING TABLES (need to be created):

CREATE TABLE api_keys (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  key_name VARCHAR(255) NOT NULL,
  api_key VARCHAR(255) UNIQUE NOT NULL,
  is_active BOOLEAN DEFAULT true,
  rate_limit INTEGER DEFAULT 1000,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  last_used_at TIMESTAMP,
  expires_at TIMESTAMP
);

CREATE TABLE api_usage (
  id SERIAL PRIMARY KEY,
  api_key_id INTEGER REFERENCES api_keys(id) ON DELETE CASCADE,
  endpoint VARCHAR(255) NOT NULL,
  request_count INTEGER DEFAULT 1,
  date DATE DEFAULT CURRENT_DATE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(api_key_id, endpoint, date)
);

// MISSING HANDLERS (need to be implemented): api.propbolt.com

mux.HandleFunc("/api/v1/user/api-keys", userAPIKeysHandler)           // GET: List user's API keys
mux.HandleFunc("/api/v1/user/api-keys/create", createAPIKeyHandler)   // POST: Create new API key
mux.HandleFunc("/api/v1/user/api-keys/delete", deleteAPIKeyHandler)   // DELETE: Delete API key
mux.HandleFunc("/api/v1/user/api-keys/usage", apiKeyUsageHandler)     // GET: Get usage statistics
mux.HandleFunc("/api/v1/user/api-keys/stats", apiKeyStatsHandler)     // GET: Get API key stats

// MISSING MIDDLEWARE: api.propbolt.com
func apiKeyAuthMiddleware(next http.Handler) http.Handler             // Validate API keys
func rateLimitMiddleware(next http.Handler) http.Handler              // Rate limiting


 NOT WORKING (MOCK DATA)
 DELETE ALL MOCK DATA.
 BUILD ONLY PRODUCTION DATA.
User API Dashboard → Shows hardcoded API keys → ❌ MOCK - build from api.propbolt.com
API Key Management → No backend endpoints → ❌ MISSING - build from brain.propbolt.com
Usage Statistics → Hardcoded numbers → ❌ MOCK - api.propbolt.com
Rate Limiting → No enforcement → ❌ MISSING - api.propbolt.com
User API Endpoints → No actual endpoints for users → api.propbolt.com - reference all internal api. do not include real estate api


Convert mock data to either be deleted or 

Remove Mock Data:
User API Dashboard - Allow for key creaton
Usage Statistics - create sql for this
Rate Limits - create sql for this
What Needs Implementation:
Database Tables - api_keys and api_usage tables
Backend Handlers - API key CRUD operations
User API Endpoints - Actual endpoints for data account users
Rate Limiting - API key validation and rate limiting
Usage Tracking - Real usage statistics and analytics


live functionality IMPLEMENTATION PRIORITY: IN PHASES

PHASE 1:
Missing User API endpoints
User Dashboard:
-API key creation
-Usage statistics
-Rate limiting

Admin Dashboard:
-API key management
-Usage statistics
-Rate limiting



The implementation would include:

Database schema for API keys and usage tracking
Backend handlers for API key management
User API endpoints for property data access
Rate limiting and authentication middleware
Frontend integration to use live data instead of mock data
This would complete the PropBolt platform and make the data account type fully functional with real API access.

