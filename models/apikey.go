package models

import (
    "crypto/rand"
    "database/sql"
    "encoding/hex"
    "fmt"
    "time"
)

// APIKey represents an API key in the database
type APIKey struct {
    ID        int       `json:"id"`
    UserID    int       `json:"user_id"`
    KeyName   string    `json:"key_name"`
    APIKey    string    `json:"api_key"`
    IsActive  bool      `json:"is_active"`
    RateLimit int       `json:"rate_limit"`
    CreatedAt time.Time `json:"created_at"`
    LastUsed  time.Time `json:"last_used_at,omitempty"`
    ExpiresAt time.Time `json:"expires_at,omitempty"`
}

// APIKeyUsage represents API usage statistics
type APIKeyUsage struct {
    ID           int       `json:"id"`
    APIKeyID     int       `json:"api_key_id"`
    Endpoint     string    `json:"endpoint"`
    RequestCount int       `json:"request_count"`
    Date         time.Time `json:"date"`
    CreatedAt    time.Time `json:"created_at"`
}

// APIKeyStats represents aggregated API key statistics
type APIKeyStats struct {
    TotalRequests     int                `json:"total_requests"`
    EndpointBreakdown map[string]int     `json:"endpoint_breakdown"`
    DailyUsage        map[string]int     `json:"daily_usage"`
    RateLimitUsage    float64            `json:"rate_limit_usage"` // Percentage of rate limit used
    APIKey            string             `json:"api_key"`
    KeyName           string             `json:"key_name"`
    IsActive          bool               `json:"is_active"`
    RateLimit         int                `json:"rate_limit"`
}

// GenerateAPIKey creates a new random API key
func GenerateAPIKey() string {
    bytes := make([]byte, 32)
    if _, err := rand.Read(bytes); err != nil {
        return ""
    }
    return hex.EncodeToString(bytes)
}

// CreateAPIKey creates a new API key for a user
func CreateAPIKey(db *sql.DB, userID int, keyName string, rateLimit int) (*APIKey, error) {
    apiKey := &APIKey{
        UserID:    userID,
        KeyName:   keyName,
        APIKey:    GenerateAPIKey(),
        IsActive:  true,
        RateLimit: rateLimit,
        CreatedAt: time.Now(),
    }

    query := `
        INSERT INTO api_keys (user_id, key_name, api_key, is_active, rate_limit, created_at)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id
    `

    err := db.QueryRow(
        query,
        apiKey.UserID,
        apiKey.KeyName,
        apiKey.APIKey,
        apiKey.IsActive,
        apiKey.RateLimit,
        apiKey.CreatedAt,
    ).Scan(&apiKey.ID)

    if err != nil {
        return nil, fmt.Errorf("failed to create API key: %v", err)
    }

    return apiKey, nil
}

// GetAPIKeysByUserID retrieves all API keys for a user
func GetAPIKeysByUserID(db *sql.DB, userID int) ([]*APIKey, error) {
    query := `
        SELECT id, user_id, key_name, api_key, is_active, rate_limit, created_at, 
               last_used_at, expires_at
        FROM api_keys
        WHERE user_id = $1
        ORDER BY created_at DESC
    `

    rows, err := db.Query(query, userID)
    if err != nil {
        return nil, fmt.Errorf("failed to query API keys: %v", err)
    }
    defer rows.Close()

    var apiKeys []*APIKey
    for rows.Next() {
        apiKey := &APIKey{}
        var lastUsed, expiresAt sql.NullTime

        err := rows.Scan(
            &apiKey.ID,
            &apiKey.UserID,
            &apiKey.KeyName,
            &apiKey.APIKey,
            &apiKey.IsActive,
            &apiKey.RateLimit,
            &apiKey.CreatedAt,
            &lastUsed,
            &expiresAt,
        )

        if err != nil {
            return nil, fmt.Errorf("failed to scan API key: %v", err)
        }

        if lastUsed.Valid {
            apiKey.LastUsed = lastUsed.Time
        }

        if expiresAt.Valid {
            apiKey.ExpiresAt = expiresAt.Time
        }

        apiKeys = append(apiKeys, apiKey)
    }

    return apiKeys, nil
}

// DeleteAPIKey deletes an API key by ID and user ID
func DeleteAPIKey(db *sql.DB, keyID, userID int) error {
    query := `DELETE FROM api_keys WHERE id = $1 AND user_id = $2`
    result, err := db.Exec(query, keyID, userID)
    if err != nil {
        return fmt.Errorf("failed to delete API key: %v", err)
    }

    rowsAffected, err := result.RowsAffected()
    if err != nil {
        return fmt.Errorf("failed to get rows affected: %v", err)
    }

    if rowsAffected == 0 {
        return fmt.Errorf("no API key found with ID %d for user %d", keyID, userID)
    }

    return nil
}

// TrackAPIUsage records API usage for an API key
func TrackAPIUsage(db *sql.DB, apiKeyID int, endpoint string) error {
    // Update last_used_at timestamp
    _, err := db.Exec(
        "UPDATE api_keys SET last_used_at = NOW() WHERE id = $1",
        apiKeyID,
    )
    if err != nil {
        return fmt.Errorf("failed to update last_used_at: %v", err)
    }

    // Upsert into api_usage table
    query := `
        INSERT INTO api_usage (api_key_id, endpoint, date)
        VALUES ($1, $2, CURRENT_DATE)
        ON CONFLICT (api_key_id, endpoint, date)
        DO UPDATE SET request_count = api_usage.request_count + 1
    `

    _, err = db.Exec(query, apiKeyID, endpoint)
    if err != nil {
        return fmt.Errorf("failed to track API usage: %v", err)
    }

    return nil
}

// GetAPIKeyUsage retrieves usage statistics for an API key
func GetAPIKeyUsage(db *sql.DB, apiKeyID int) ([]*APIKeyUsage, error) {
    query := `
        SELECT id, api_key_id, endpoint, request_count, date, created_at
        FROM api_usage
        WHERE api_key_id = $1
        ORDER BY date DESC, endpoint
    `

    rows, err := db.Query(query, apiKeyID)
    if err != nil {
        return nil, fmt.Errorf("failed to query API usage: %v", err)
    }
    defer rows.Close()

    var usageStats []*APIKeyUsage
    for rows.Next() {
        usage := &APIKeyUsage{}
        err := rows.Scan(
            &usage.ID,
            &usage.APIKeyID,
            &usage.Endpoint,
            &usage.RequestCount,
            &usage.Date,
            &usage.CreatedAt,
        )

        if err != nil {
            return nil, fmt.Errorf("failed to scan API usage: %v", err)
        }

        usageStats = append(usageStats, usage)
    }

    return usageStats, nil
}

// GetAPIKeyStats retrieves aggregated statistics for an API key
func GetAPIKeyStats(db *sql.DB, apiKeyID int) (*APIKeyStats, error) {
    // Get API key details
    apiKey := &APIKey{}
    err := db.QueryRow(`
        SELECT id, user_id, key_name, api_key, is_active, rate_limit
        FROM api_keys
        WHERE id = $1
    `, apiKeyID).Scan(
        &apiKey.ID,
        &apiKey.UserID,
        &apiKey.KeyName,
        &apiKey.APIKey,
        &apiKey.IsActive,
        &apiKey.RateLimit,
    )
    if err != nil {
        return nil, fmt.Errorf("failed to get API key: %v", err)
    }

    // Get total requests
    var totalRequests int
    err = db.QueryRow(`
        SELECT COALESCE(SUM(request_count), 0)
        FROM api_usage
        WHERE api_key_id = $1
    `, apiKeyID).Scan(&totalRequests)
    if err != nil {
        return nil, fmt.Errorf("failed to get total requests: %v", err)
    }

    // Get endpoint breakdown
    rows, err := db.Query(`
        SELECT endpoint, SUM(request_count) as count
        FROM api_usage
        WHERE api_key_id = $1
        GROUP BY endpoint
        ORDER BY count DESC
    `, apiKeyID)
    if err != nil {
        return nil, fmt.Errorf("failed to get endpoint breakdown: %v", err)
    }
    defer rows.Close()

    endpointBreakdown := make(map[string]int)
    for rows.Next() {
        var endpoint string
        var count int
        if err := rows.Scan(&endpoint, &count); err != nil {
            return nil, fmt.Errorf("failed to scan endpoint breakdown: %v", err)
        }
        endpointBreakdown[endpoint] = count
    }

    // Get daily usage
    dailyRows, err := db.Query(`
        SELECT TO_CHAR(date, 'YYYY-MM-DD') as day, SUM(request_count) as count
        FROM api_usage
        WHERE api_key_id = $1
        GROUP BY date
        ORDER BY date DESC
        LIMIT 30
    `, apiKeyID)
    if err != nil {
        return nil, fmt.Errorf("failed to get daily usage: %v", err)
    }
    defer dailyRows.Close()

    dailyUsage := make(map[string]int)
    for dailyRows.Next() {
        var day string
        var count int
        if err := dailyRows.Scan(&day, &count); err != nil {
            return nil, fmt.Errorf("failed to scan daily usage: %v", err)
        }
        dailyUsage[day] = count
    }

    // Calculate rate limit usage (based on today's usage)
    var todayUsage int
    err = db.QueryRow(`
        SELECT COALESCE(SUM(request_count), 0)
        FROM api_usage
        WHERE api_key_id = $1 AND date = CURRENT_DATE
    `, apiKeyID).Scan(&todayUsage)
    if err != nil {
        return nil, fmt.Errorf("failed to get today's usage: %v", err)
    }

    rateLimitUsage := float64(todayUsage) / float64(apiKey.RateLimit)

    return &APIKeyStats{
        TotalRequests:     totalRequests,
        EndpointBreakdown: endpointBreakdown,
        DailyUsage:        dailyUsage,
        RateLimitUsage:    rateLimitUsage,
        APIKey:            apiKey.APIKey,
        KeyName:           apiKey.KeyName,
        IsActive:          apiKey.IsActive,
        RateLimit:         apiKey.RateLimit,
    }, nil
}

// ValidateAPIKey checks if an API key is valid and returns the API key ID
func ValidateAPIKey(db *sql.DB, apiKey string) (int, int, error) {
    var id, userID int
    var isActive bool
    var rateLimit int

    err := db.QueryRow(`
        SELECT id, user_id, is_active, rate_limit
        FROM api_keys
        WHERE api_key = $1
    `, apiKey).Scan(&id, &userID, &isActive, &rateLimit)

    if err != nil {
        if err == sql.ErrNoRows {
            return 0, 0, fmt.Errorf("invalid API key")
        }
        return 0, 0, fmt.Errorf("database error: %v", err)
    }

    if !isActive {
        return 0, 0, fmt.Errorf("API key is inactive")
    }

    // Check rate limit
    var todayUsage int
    err = db.QueryRow(`
        SELECT COALESCE(SUM(request_count), 0)
        FROM api_usage
        WHERE api_key_id = $1 AND date = CURRENT_DATE
    `, id).Scan(&todayUsage)

    if err != nil {
        return 0, 0, fmt.Errorf("failed to check rate limit: %v", err)
    }

    if todayUsage >= rateLimit {
        return 0, 0, fmt.Errorf("rate limit exceeded")
    }

    return id, userID, nil
}