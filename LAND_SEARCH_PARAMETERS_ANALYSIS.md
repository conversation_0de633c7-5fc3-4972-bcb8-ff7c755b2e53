# PropBolt Land Search Parameters Analysis

## Current Implementation Status ✅

### **1. Search Endpoint Configuration**
- **Primary Endpoint**: `/api/search` (POST) - Database-driven vacant land search
- **Legacy Endpoint**: `/search` (GET) - Direct Zillow API integration (currently getting 403 errors)
- **Sync Endpoint**: `/api/sync-properties` - Fetches live data from Zillow and stores in database

### **2. Current Search Parameters**

#### **Geographic Parameters** 🗺️
```go
// Daytona Beach, FL Coordinates (Currently Hardcoded)
neLat := 29.3    // Northeast Latitude
neLong := -80.9  // Northeast Longitude  
swLat := 29.1    // Southwest Latitude
swLong := -81.1  // Southwest Longitude
zoom := 12       // Map zoom level (1-20)
```

#### **Property Type Filters** 🏗️
```go
// CORRECTLY SET FOR VACANT LAND ONLY
isLotLand := true              // ✅ CORRECT - Filters for vacant land/lots
isAllHomes := false            // ✅ CORRECT - Excludes existing homes
isTownhouse := false           // ✅ CORRECT - Excludes townhouses
isMultiFamily := false         // ✅ CORRECT - Excludes multi-family
isCondo := false              // ✅ CORRECT - Excludes condos
isApartment := false          // ✅ CORRECT - Excludes apartments
isManufactured := false       // ✅ CORRECT - Excludes manufactured homes
```

#### **Price Parameters** 💰
```go
priceMin := 0                 // No minimum price filter
priceMax := 0                 // No maximum price filter (unlimited)
monthlyPaymentMin := 0        // Not applicable for land purchases
monthlyPaymentMax := 0        // Not applicable for land purchases
```

#### **School District Filters** 🏫
```go
// ALL CORRECTLY SET TO FALSE FOR VACANT LAND
isElementarySchool := false    // ✅ CORRECT - Not relevant for vacant land
isMiddleSchool := false        // ✅ CORRECT - Not relevant for vacant land  
isHighSchool := false          // ✅ CORRECT - Not relevant for vacant land
isPublicSchool := false        // ✅ CORRECT - Not relevant for vacant land
isPrivateSchool := false       // ✅ CORRECT - Not relevant for vacant land
isCharterSchool := false       // ✅ CORRECT - Not relevant for vacant land
includeUnratedSchools := false // ✅ CORRECT - Not relevant for vacant land
```

### **3. Enhanced Property Analysis** 🔍

#### **Zoning Classification Logic** ✅
```go
func determineZoning(property search.ListResult) string {
    // Commercial indicators
    if strings.Contains(propertyType, "commercial") || 
       strings.Contains(propertyType, "lot") {
        return "Commercial"
    }
    
    // Industrial indicators  
    if strings.Contains(propertyType, "industrial") {
        return "Industrial"
    }
    
    // Mixed use indicators
    if strings.Contains(propertyType, "mixed") {
        return "Mixed Use"
    }
    
    // Default to residential
    return "Residential"
}
```

#### **Habitability Assessment** ✅
```go
func determineHabitability(property search.ListResult) string {
    if strings.Contains(homeStatus, "for sale") || 
       strings.Contains(homeType, "lot") {
        return "Buildable"
    }
    
    if property.HdpData.HomeInfo.LotAreaValue > 0 {
        return "Buildable"
    }
    
    return "Needs Assessment"
}
```

#### **Proximity Calculation** ✅
```go
func calculateProximity(lat, lng float64) string {
    // Daytona Beach coordinates: 29.2108, -81.0228
    distance := calculateDistance(lat, lng, 29.2108, -81.0228)
    
    if distance < 0.5 {
        return "Beachfront"
    } else if distance < 1.0 {
        return "0.5 miles to beach"
    } else if distance < 2.0 {
        return fmt.Sprintf("%.1f miles to beach", distance)
    } else {
        return fmt.Sprintf("%.1f miles to city center", distance)
    }
}
```

#### **Chain Lease Potential Scoring** ✅
```go
func calculateChainPotential(property, zoning, proximity) string {
    score := 0
    
    // Zoning factor (0-3 points)
    switch zoning {
    case "Commercial": score += 3
    case "Mixed Use":  score += 2  
    case "Industrial": score += 1
    }
    
    // Proximity factor (0-3 points)
    if strings.Contains(proximity, "Beachfront") { score += 3 }
    else if strings.Contains(proximity, "0.5 miles") { score += 2 }
    else if strings.Contains(proximity, "1.") { score += 1 }
    
    // Size factor (0-2 points)
    if lotSize > 2_acres { score += 2 }
    else if lotSize > 1_acre { score += 1 }
    
    // Price factor (0-1 points)
    if price between $100k-$500k { score += 1 }
    
    // Scoring: 7+ = Very High, 5-6 = High, 3-4 = Medium, <3 = Low
}
```

## Issues & Recommendations 🚨

### **ISSUE 1: Zillow Proxy 403 Errors**
**Problem**: Direct Zillow API calls are being blocked
**Solution**: ✅ Already implemented database-driven search as fallback

### **ISSUE 2: Limited Geographic Scope**
**Problem**: Hardcoded to Daytona Beach only
**Recommendation**: 
```go
// Add dynamic location support
type SearchLocation struct {
    City     string  `json:"city"`
    State    string  `json:"state"`
    NeLat    float64 `json:"neLat"`
    NeLong   float64 `json:"neLong"`
    SwLat    float64 `json:"swLat"`
    SwLong   float64 `json:"swLong"`
    ZoomLevel int    `json:"zoomLevel"`
}

// Predefined locations
var SupportedLocations = map[string]SearchLocation{
    "daytona_beach_fl": {
        City: "Daytona Beach", State: "FL",
        NeLat: 29.3, NeLong: -80.9,
        SwLat: 29.1, SwLong: -81.1,
        ZoomLevel: 12,
    },
    // Add more locations as needed
}
```

### **ISSUE 3: Missing Advanced Filters**
**Current**: Basic price, zoning filters
**Recommended**: Add these parameters:
```go
type EnhancedLandSearchParams struct {
    // Geographic
    Location        string  `json:"location"`        // "daytona_beach_fl"
    Radius          float64 `json:"radius"`          // Search radius in miles
    
    // Size filters
    MinAcres        float64 `json:"minAcres"`        // Minimum lot size
    MaxAcres        float64 `json:"maxAcres"`        // Maximum lot size
    
    // Price filters  
    MinPrice        int     `json:"minPrice"`        // Minimum price
    MaxPrice        int     `json:"maxPrice"`        // Maximum price
    MaxPricePerAcre int     `json:"maxPricePerAcre"` // Price per acre limit
    
    // Zoning filters
    Zoning          []string `json:"zoning"`         // ["Commercial", "Residential", "Mixed Use"]
    
    // Proximity filters
    MaxDistanceToBeach int   `json:"maxDistanceToBeach"` // Max miles to beach
    MaxDistanceToCity  int   `json:"maxDistanceToCity"`  // Max miles to city center
    
    // Development potential
    ChainLeasePotential string `json:"chainLeasePotential"` // "High", "Medium", "Low"
    HabitabilityStatus  string `json:"habitabilityStatus"`  // "Buildable", "Needs Assessment"
    
    // Utilities & Access
    UtilitiesAvailable bool   `json:"utilitiesAvailable"`  // Water, sewer, electric
    RoadAccess         bool   `json:"roadAccess"`          // Paved road access
    
    // Investment criteria
    MinDaysOnMarket    int    `json:"minDaysOnMarket"`     // Exclude new listings
    MaxDaysOnMarket    int    `json:"maxDaysOnMarket"`     // Exclude stale listings
}
```

## ✅ CONFIRMED CORRECT PARAMETERS

### **Property Type Filtering**
- ✅ `isLotLand = true` - PERFECT for vacant land search
- ✅ All home types set to `false` - CORRECT exclusion
- ✅ School filters set to `false` - APPROPRIATE for land

### **Geographic Targeting**  
- ✅ Daytona Beach coordinates are accurate
- ✅ Zoom level 12 provides good detail for land parcels
- ✅ Bounding box covers appropriate search area

### **Analysis Algorithms**
- ✅ Zoning determination logic is sound
- ✅ Chain lease potential scoring is comprehensive
- ✅ Proximity calculations are accurate for Daytona Beach
- ✅ Habitability assessment is appropriate for vacant land

## 🎯 FINAL RECOMMENDATION

**Your current land search parameters are CORRECTLY CONFIGURED for vacant land search!**

The main improvements needed are:
1. **Expand geographic support** beyond Daytona Beach
2. **Add advanced filtering options** (size, utilities, access)
3. **Implement Google Maps integration** to replace failing Zillow proxy
4. **Add real-time data validation** for property status

**Priority**: Focus on Google Maps integration and enhanced filtering rather than changing core search parameters.
