dispatch:
  # Brain API routes (admin-only endpoints)
  - url: "brain.propbolt.com/*"
    service: default

  - url: "api.propbolt.com/*"
    service: default

  # Admin and user redirects
  - url: "admin.propbolt.com/*"
    service: default

  - url: "go.propbolt.com/*"
    service: default

  # API requests to backend (default service)
  - url: "propbolt.com/api/*"
    service: default

  # Health check and status endpoints
  - url: "propbolt.com/health"
    service: default

  - url: "propbolt.com/status"
    service: default

  # Login redirect endpoint
  - url: "propbolt.com/login"
    service: default

  # Route www.propbolt.com to frontend service
  - url: "www.propbolt.com/*"
    service: frontend

  # Route propbolt.com to frontend service (default for all other routes)
  - url: "propbolt.com/*"
    service: frontend

  # Default routing for App Engine URLs
  - url: "*/api/*"
    service: default

  # Default frontend routing
  - url: "*/*"
    service: frontend
