package main

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"propbolt/realestate"
	"time"
)

// TestResult represents the result of an API endpoint test
type TestResult struct {
	Endpoint    string        `json:"endpoint"`
	Success     bool          `json:"success"`
	Duration    time.Duration `json:"duration"`
	Error       string        `json:"error,omitempty"`
	ResponseSize int          `json:"responseSize"`
	TestData    interface{}   `json:"testData,omitempty"`
}

// VacantLandSearchParams represents comprehensive vacant land search parameters
type VacantLandSearchParams struct {
	// Geographic Parameters
	City      string  `json:"city"`
	State     string  `json:"state"`
	ZipCode   string  `json:"zipCode"`
	County    string  `json:"county"`
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
	Radius    float64 `json:"radius"`

	// Property Type Filters (Vacant Land Specific)
	PropertyType     string   `json:"propertyType"`     // "land", "vacant_land"
	LandUse         []string `json:"landUse"`          // "residential", "commercial", "agricultural", "industrial"
	ZoningTypes     []string `json:"zoningTypes"`      // "R1", "R2", "C1", "A1", etc.
	
	// Size Filters
	MinLotSize      int     `json:"minLotSize"`       // Square feet
	MaxLotSize      int     `json:"maxLotSize"`       // Square feet
	MinAcres        float64 `json:"minAcres"`
	MaxAcres        float64 `json:"maxAcres"`
	
	// Price Filters
	MinPrice        int     `json:"minPrice"`
	MaxPrice        int     `json:"maxPrice"`
	PricePerSqft    float64 `json:"pricePerSqft"`
	
	// Utilities & Infrastructure
	HasUtilities    bool    `json:"hasUtilities"`
	HasWater        bool    `json:"hasWater"`
	HasSewer        bool    `json:"hasSewer"`
	HasElectric     bool    `json:"hasElectric"`
	HasGas          bool    `json:"hasGas"`
	HasRoadAccess   bool    `json:"hasRoadAccess"`
	
	// Environmental & Location Features
	IsWaterfront    bool    `json:"isWaterfront"`
	HasView         bool    `json:"hasView"`
	IsBuildable     bool    `json:"isBuildable"`
	FloodZone       string  `json:"floodZone"`
	
	// Market Filters
	DaysOnMarket    int     `json:"daysOnMarket"`
	ListingStatus   string  `json:"listingStatus"`    // "active", "pending", "sold"
	MLSActive       bool    `json:"mlsActive"`
	
	// Search Parameters
	Size            int     `json:"size"`             // Number of results
	From            int     `json:"from"`             // Pagination offset
	SortBy          string  `json:"sortBy"`           // "price", "size", "date"
	SortOrder       string  `json:"sortOrder"`        // "asc", "desc"
}

// TestRunner manages all API endpoint tests
type TestRunner struct {
	client  *realestate.Client
	results []TestResult
}

// NewTestRunner creates a new test runner
func NewTestRunner() *TestRunner {
	return &TestRunner{
		client:  realestate.NewClient(),
		results: make([]TestResult, 0),
	}
}

// runTest executes a single test and records the result
func (tr *TestRunner) runTest(endpoint string, testFunc func() (interface{}, error)) {
	start := time.Now()
	
	log.Printf("🧪 Testing endpoint: %s", endpoint)
	
	data, err := testFunc()
	duration := time.Since(start)
	
	result := TestResult{
		Endpoint: endpoint,
		Success:  err == nil,
		Duration: duration,
		TestData: data,
	}
	
	if err != nil {
		result.Error = err.Error()
		log.Printf("❌ %s failed: %v", endpoint, err)
	} else {
		log.Printf("✅ %s succeeded in %v", endpoint, duration)
		if data != nil {
			if jsonData, err := json.Marshal(data); err == nil {
				result.ResponseSize = len(jsonData)
			}
		}
	}
	
	tr.results = append(tr.results, result)
}

// TestAutoComplete tests the AutoComplete endpoint with various inputs
func (tr *TestRunner) TestAutoComplete() {
	testCases := []string{
		"Daytona Beach, FL",
		"32114",
		"Volusia County, FL",
		"123 Main Street, Daytona Beach, FL",
		"Florida",
	}
	
	for _, input := range testCases {
		tr.runTest(fmt.Sprintf("AutoComplete(%s)", input), func() (interface{}, error) {
			return tr.client.AutoComplete(input)
		})
	}
}

// TestPropertyMapping tests the Property Mapping endpoint
func (tr *TestRunner) TestPropertyMapping() {
	// Test with Daytona Beach coordinates
	query := map[string]interface{}{
		"city":         "Daytona Beach",
		"state":        "FL",
		"propertyType": "land",
		"bounds": map[string]float64{
			"north": 29.3,
			"south": 29.1,
			"east":  -80.9,
			"west":  -81.1,
		},
	}
	
	tr.runTest("PropertyMapping", func() (interface{}, error) {
		return tr.client.PropertyMapping(query)
	})
}

// TestPropertyDetail tests the Property Detail endpoint
func (tr *TestRunner) TestPropertyDetail() {
	testAddresses := []string{
		"123 Main Street, Daytona Beach, FL 32114",
		"456 Ocean Avenue, Daytona Beach, FL",
	}
	
	for _, address := range testAddresses {
		tr.runTest(fmt.Sprintf("PropertyDetail(%s)", address), func() (interface{}, error) {
			return tr.client.PropertyDetail(address, "", true)
		})
	}
}

// TestPropertySearch tests the Property Search endpoint with vacant land parameters
func (tr *TestRunner) TestPropertySearch() {
	// Test Case 1: Basic vacant land search in Daytona Beach
	query1 := map[string]interface{}{
		"city":         "Daytona Beach",
		"state":        "FL",
		"propertyType": "land",
		"listingStatus": "active",
		"minPrice":     10000,
		"maxPrice":     500000,
		"minLotSize":   5000,  // 5000 sq ft minimum
		"hasUtilities": true,
	}
	
	tr.runTest("PropertySearch(VacantLand-Basic)", func() (interface{}, error) {
		return tr.client.PropertySearch(query1, 25, 0, false)
	})
	
	// Test Case 2: Advanced vacant land search with specific criteria
	query2 := map[string]interface{}{
		"zipCode":      "32114",
		"propertyType": "land",
		"minAcres":     0.5,
		"maxAcres":     5.0,
		"isBuildable":  true,
		"hasRoadAccess": true,
		"floodZone":    "X", // Not in flood zone
	}
	
	tr.runTest("PropertySearch(VacantLand-Advanced)", func() (interface{}, error) {
		return tr.client.PropertySearch(query2, 50, 0, false)
	})
	
	// Test Case 3: Coordinate-based search
	query3 := map[string]interface{}{
		"latitude":     29.2108,
		"longitude":    -81.0228,
		"radius":       10, // 10 miles
		"propertyType": "land",
		"sortBy":       "price",
		"sortOrder":    "asc",
	}
	
	tr.runTest("PropertySearch(Coordinates)", func() (interface{}, error) {
		return tr.client.PropertySearch(query3, 25, 0, false)
	})
}

// TestInvoluntaryLiens tests the Involuntary Liens endpoint
func (tr *TestRunner) TestInvoluntaryLiens() {
	testAddress := "123 Main Street, Daytona Beach, FL 32114"
	
	tr.runTest("InvoluntaryLiens", func() (interface{}, error) {
		return tr.client.InvoluntaryLiens(testAddress, "")
	})
}

// TestPropertyComps tests both v2 and v3 Property Comps endpoints
func (tr *TestRunner) TestPropertyComps() {
	testAddress := "123 Main Street, Daytona Beach, FL 32114"
	
	// Test v2
	tr.runTest("PropertyCompsV2", func() (interface{}, error) {
		return tr.client.PropertyCompsV2(testAddress, 10, 2.0)
	})
	
	// Test v3 with custom parameters for land
	customParams := map[string]interface{}{
		"propertyType": "land",
		"minLotSize":   1000,
	}
	
	tr.runTest("PropertyCompsV3", func() (interface{}, error) {
		return tr.client.PropertyCompsV3(testAddress, customParams, nil, 15, 3.0)
	})
}

// TestSkipTrace tests the SkipTrace endpoint
func (tr *TestRunner) TestSkipTrace() {
	tr.runTest("SkipTrace", func() (interface{}, error) {
		return tr.client.SkipTrace("John", "Doe", "123 Main Street", "Daytona Beach", "FL", "32114")
	})
}

// RunAllTests executes all endpoint tests
func (tr *TestRunner) RunAllTests() {
	log.Println("🚀 Starting comprehensive RealEstateAPI endpoint testing...")
	log.Println("📍 Focus: Vacant Land Search Capabilities")
	
	tr.TestAutoComplete()
	tr.TestPropertyMapping()
	tr.TestPropertyDetail()
	tr.TestPropertySearch()
	tr.TestInvoluntaryLiens()
	tr.TestPropertyComps()
	tr.TestSkipTrace()
	
	tr.generateReport()
}

// generateReport creates a comprehensive test report
func (tr *TestRunner) generateReport() {
	log.Println("\n📊 TEST RESULTS SUMMARY")
	log.Println("=" * 50)
	
	totalTests := len(tr.results)
	successfulTests := 0
	totalDuration := time.Duration(0)
	
	for _, result := range tr.results {
		if result.Success {
			successfulTests++
		}
		totalDuration += result.Duration
		
		status := "✅"
		if !result.Success {
			status = "❌"
		}
		
		log.Printf("%s %s (%.2fs)", status, result.Endpoint, result.Duration.Seconds())
		if result.Error != "" {
			log.Printf("   Error: %s", result.Error)
		}
		if result.ResponseSize > 0 {
			log.Printf("   Response Size: %d bytes", result.ResponseSize)
		}
	}
	
	log.Printf("\n📈 SUMMARY:")
	log.Printf("Total Tests: %d", totalTests)
	log.Printf("Successful: %d", successfulTests)
	log.Printf("Failed: %d", totalTests-successfulTests)
	log.Printf("Success Rate: %.1f%%", float64(successfulTests)/float64(totalTests)*100)
	log.Printf("Total Duration: %.2fs", totalDuration.Seconds())
	
	// Save detailed results to JSON file
	tr.saveResultsToFile()
}

// saveResultsToFile saves test results to a JSON file
func (tr *TestRunner) saveResultsToFile() {
	filename := fmt.Sprintf("realestate_api_test_results_%s.json", time.Now().Format("20060102_150405"))
	
	jsonData, err := json.MarshalIndent(tr.results, "", "  ")
	if err != nil {
		log.Printf("Error marshaling results: %v", err)
		return
	}
	
	err = os.WriteFile(filename, jsonData, 0644)
	if err != nil {
		log.Printf("Error writing results file: %v", err)
		return
	}
	
	log.Printf("📄 Detailed results saved to: %s", filename)
}

func main() {
	// Ensure API key is set
	if os.Getenv("REAL_ESTATE_API_KEY") == "" {
		log.Fatal("REAL_ESTATE_API_KEY environment variable is required")
	}
	
	runner := NewTestRunner()
	runner.RunAllTests()
}
