#!/bin/bash

# PropBolt Production Deployment Script
# Comprehensive deployment with database setup, SSL certificates, and monitoring
# Deploys both backend (Go) and frontend (Next.js) to Google App Engine

set -e  # Exit on any error

PROJECT_ID="gold-braid-458901-v2"
DB_INSTANCE="propbolt-postgres"
DB_NAME="propbolt"
DB_USER="propbolt_user"
DB_PASSWORD="PropboltSecure2024!"
DOMAINS=("propbolt.com" "www.propbolt.com" "brain.propbolt.com" "api.propbolt.com" "admin.propbolt.com" "go.propbolt.com")

echo "🚀 Starting PropBolt Production Deployment..."
echo "📋 Project: $PROJECT_ID"
echo "🌐 Domains: ${DOMAINS[*]}"
echo ""

# Set the project
echo "🔧 Setting Google Cloud project..."
sudo gcloud config set project $PROJECT_ID

# Enable required APIs first
echo "🔌 Enabling required Google Cloud APIs..."
sudo gcloud services enable appengine.googleapis.com --project=$PROJECT_ID
sudo gcloud services enable sqladmin.googleapis.com --project=$PROJECT_ID
sudo gcloud services enable cloudresourcemanager.googleapis.com --project=$PROJECT_ID
sudo gcloud services enable compute.googleapis.com --project=$PROJECT_ID
sudo gcloud services enable logging.googleapis.com --project=$PROJECT_ID
sudo gcloud services enable monitoring.googleapis.com --project=$PROJECT_ID

# Check if Cloud SQL instance exists, create if not
echo "🗄️ Setting up Google Cloud SQL PostgreSQL..."
if ! sudo gcloud sql instances describe $DB_INSTANCE --project=$PROJECT_ID >/dev/null 2>&1; then
    echo "Creating Cloud SQL PostgreSQL instance..."
    sudo gcloud sql instances create $DB_INSTANCE \
        --database-version=POSTGRES_15 \
        --tier=db-f1-micro \
        --region=us-central1 \
        --storage-type=SSD \
        --storage-size=10GB \
        --storage-auto-increase \
        --backup-start-time=03:00 \
        --enable-bin-log \
        --require-ssl \
        --project=$PROJECT_ID

    echo "Setting database password..."
    sudo gcloud sql users set-password postgres \
        --instance=$DB_INSTANCE \
        --password="$DB_PASSWORD" \
        --project=$PROJECT_ID

    echo "Creating application database..."
    sudo gcloud sql databases create $DB_NAME \
        --instance=$DB_INSTANCE \
        --project=$PROJECT_ID

    echo "Creating application user..."
    sudo gcloud sql users create $DB_USER \
        --instance=$DB_INSTANCE \
        --password="$DB_PASSWORD" \
        --project=$PROJECT_ID
else
    echo "✅ Cloud SQL instance already exists"
fi

# Clean up Go dependencies and build
echo "🧹 Cleaning Go dependencies..."
sudo go mod tidy

echo "🔨 Building Go backend..."
sudo go build -buildvcs=false -o propbolt .

# Build Next.js frontend
echo "🏗️ Building Next.js frontend..."
sudo npm run build

# Prepare deployment files
echo "📁 Preparing deployment files..."

# Copy backend .gcloudignore for backend deployment
cp .gcloudignore .gcloudignore-backend-temp

# Deploy backend (Go API) first
echo "🔧 Deploying Go backend to default service..."
sudo gcloud app deploy app.yaml --quiet --project=$PROJECT_ID

# Switch to frontend .gcloudignore for frontend deployment
echo "📁 Switching to frontend deployment configuration..."
cp .gcloudignore-frontend .gcloudignore

# Deploy frontend (Next.js)
echo "🎨 Deploying Next.js frontend to frontend service..."
sudo gcloud app deploy frontend-app.yaml --quiet --project=$PROJECT_ID

# Restore backend .gcloudignore
cp .gcloudignore-backend-temp .gcloudignore
rm .gcloudignore-backend-temp

# Deploy dispatch configuration
echo "🔀 Deploying dispatch routing configuration..."
sudo gcloud app deploy dispatch.yaml --quiet --project=$PROJECT_ID

# Set up custom domains and SSL certificates
echo "🔒 Setting up custom domains and SSL certificates..."

# Configure SSL certificates for all 6 PropBolt domains
echo "🔒 Configuring SSL certificates for all PropBolt domains..."
echo "📋 Domains to configure: ${DOMAINS[*]}"
echo ""

for domain in "${DOMAINS[@]}"; do
    echo "🌐 Processing domain: $domain"

    # Create domain mapping first
    echo "  🗺️ Creating domain mapping..."
    if sudo gcloud app domain-mappings create $domain \
        --project=$PROJECT_ID \
        --quiet 2>/dev/null; then
        echo "  ✅ Domain mapping created for $domain"
    else
        echo "  ✅ Domain mapping for $domain already exists"
    fi

    # Create SSL certificate with proper naming
    cert_name="${domain//./-}-ssl-cert"
    echo "  🔒 Creating SSL certificate: $cert_name"

    if sudo gcloud app ssl-certificates create "$cert_name" \
        --domains=$domain \
        --project=$PROJECT_ID \
        --quiet 2>/dev/null; then
        echo "  ✅ SSL certificate created for $domain"
    else
        echo "  ✅ SSL certificate for $domain already exists or in progress"
    fi

    # Verify certificate status
    echo "  📊 Checking certificate status..."
    sudo gcloud app ssl-certificates describe "$cert_name" \
        --project=$PROJECT_ID \
        --format="value(domainMappingsCount,managedCertificate.status)" 2>/dev/null || \
        echo "  ⏳ Certificate status pending..."

    echo ""
done

echo "🎉 SSL certificate configuration completed for all domains!"
echo ""
echo "📋 SSL Certificate Summary:"
echo "  1. propbolt.com → propbolt-com-ssl-cert"
echo "  2. www.propbolt.com → www-propbolt-com-ssl-cert"
echo "  3. brain.propbolt.com → brain-propbolt-com-ssl-cert"
echo "  4. api.propbolt.com → api-propbolt-com-ssl-cert"
echo "  5. admin.propbolt.com → admin-propbolt-com-ssl-cert"
echo "  6. go.propbolt.com → go-propbolt-com-ssl-cert"
echo ""
echo "⏳ SSL certificates are being provisioned and may take up to 15 minutes to become active."
echo "🔍 Monitor certificate status: sudo gcloud app ssl-certificates list --project=$PROJECT_ID"

# Configure firewall rules for App Engine
echo "🔥 Configuring firewall rules..."
sudo gcloud compute firewall-rules create allow-app-engine-https \
    --allow tcp:443 \
    --source-ranges 0.0.0.0/0 \
    --description "Allow HTTPS traffic to App Engine" \
    --project=$PROJECT_ID \
    --quiet || echo "✅ Firewall rule already exists"

sudo gcloud compute firewall-rules create allow-app-engine-http \
    --allow tcp:80 \
    --source-ranges 0.0.0.0/0 \
    --description "Allow HTTP traffic to App Engine (redirects to HTTPS)" \
    --project=$PROJECT_ID \
    --quiet || echo "✅ Firewall rule already exists"

# Set up monitoring and logging
echo "📊 Setting up monitoring and logging..."

# Create BigQuery dataset for logs if it doesn't exist
sudo gcloud bigquery datasets create propbolt_logs \
    --location=US \
    --project=$PROJECT_ID \
    --quiet || echo "✅ BigQuery dataset already exists"

# Create logging sink
sudo gcloud logging sinks create propbolt-app-logs \
    bigquery.googleapis.com/projects/$PROJECT_ID/datasets/propbolt_logs \
    --log-filter='resource.type="gae_app"' \
    --project=$PROJECT_ID \
    --quiet || echo "✅ Logging sink already exists"

# Set up monitoring alerts
echo "🚨 Setting up monitoring alerts..."
# Basic monitoring is automatically enabled for App Engine

# Verify SSL certificate deployment
echo ""
echo "🔍 Verifying SSL certificate deployment..."
echo "📋 Checking all 6 PropBolt domain certificates:"
echo ""

for domain in "${DOMAINS[@]}"; do
    cert_name="${domain//./-}-ssl-cert"
    echo "🔒 Checking $domain ($cert_name):"

    # Check if certificate exists and get status
    if cert_info=$(sudo gcloud app ssl-certificates describe "$cert_name" \
        --project=$PROJECT_ID \
        --format="value(managedCertificate.status,domainMappingsCount)" 2>/dev/null); then

        status=$(echo "$cert_info" | cut -d$'\t' -f1)
        mappings=$(echo "$cert_info" | cut -d$'\t' -f2)

        case "$status" in
            "ACTIVE")
                echo "  ✅ ACTIVE - SSL certificate is working"
                ;;
            "PROVISIONING")
                echo "  ⏳ PROVISIONING - Certificate is being created (up to 15 minutes)"
                ;;
            "FAILED_RETRYING_NOT_VISIBLE")
                echo "  🔄 RETRYING - Google is retrying certificate creation"
                ;;
            "FAILED_RETRYING_CAA_FORBIDDEN")
                echo "  ⚠️  CAA_FORBIDDEN - DNS CAA record may be blocking certificate"
                ;;
            "FAILED_RETRYING_CAA_CHECKING")
                echo "  🔍 CAA_CHECKING - Google is checking DNS CAA records"
                ;;
            *)
                echo "  📊 STATUS: $status"
                ;;
        esac

        echo "  📊 Domain mappings: $mappings"
    else
        echo "  ❌ Certificate not found or error occurred"
    fi
    echo ""
done

echo "🎯 SSL Certificate Management Commands:"
echo "  List all certificates: sudo gcloud app ssl-certificates list --project=$PROJECT_ID"
echo "  Check specific cert: sudo gcloud app ssl-certificates describe CERT_NAME --project=$PROJECT_ID"
echo "  Delete certificate: sudo gcloud app ssl-certificates delete CERT_NAME --project=$PROJECT_ID"
echo ""

# Display deployment information
echo ""
echo "🎉 Deployment completed successfully!"
echo ""
echo "📍 Your application is now available at:"
echo "   🌐 Frontend: https://propbolt.com"
echo "   🔌 API: https://propbolt.com/api"
echo ""
echo "🔒 SSL certificates are being provisioned and may take up to 15 minutes to become active."
echo ""
echo "📊 Monitor your application:"
echo "   Logs: https://console.cloud.google.com/logs/query?project=$PROJECT_ID"
echo "   Monitoring: https://console.cloud.google.com/monitoring?project=$PROJECT_ID"
echo "   App Engine: https://console.cloud.google.com/appengine?project=$PROJECT_ID"
echo ""
echo "🔧 Useful commands:"
echo "   View logs: sudo gcloud app logs tail -s default --project=$PROJECT_ID"
echo "   View frontend logs: sudo gcloud app logs tail -s frontend --project=$PROJECT_ID"
echo "   Check SSL status: sudo gcloud app ssl-certificates list --project=$PROJECT_ID"
echo ""

# Test the deployment
echo "🧪 Testing deployment..."
echo "Testing backend health..."
curl -f https://propbolt.com/health || echo "⚠️  Backend health check failed (may be normal during initial deployment)"

echo "Testing frontend..."
curl -f https://propbolt.com || echo "⚠️  Frontend check failed (may be normal during initial deployment)"

echo ""
echo "✅ Production deployment complete! Your PropBolt application is live with SSL certificates."
