# Project Configuration

## Project Identity
**Project Name**: V1-GO Real Estate Search Engine  
**Project Type**: Independent Real Estate Data Aggregation Platform  
**Repository**: br<PERSON><PERSON><PERSON>ens/v1-go

## IMPORTANT: Project Separation Notice

⚠️ **CRITICAL**: This project is **ENTIRELY SEPARATE** from PropBolt.

### Project Boundaries
- **This Project (V1-GO)**: Independent real estate search and data aggregation platform
- **PropBolt**: Separate real estate admin panel project (DO NOT MODIFY)
- **No Cross-Contamination**: These projects must remain completely isolated

### Google Cloud Configuration
- **Project ID**: `gold-braid-458901-v2`
- **Project Number**: `456078002475`
- **Services**:
  - Backend: 'default' service at api.propbolt.com
  - Frontend: 'frontend' service at propbolt.com

### Development Environment
- **Frontend Dev**: `localhost:3000` (Next.js)
- **Backend Dev**: `localhost:8080` (Go)
- **Database**: Google Cloud SQL PostgreSQL (`propbolt-postgres`)
- **Database Status**: ✅ Google Cloud SQL PostgreSQL (fully migrated)
- **Connection**: Uses Cloud SQL socket in production, local proxy for development

### Authentication System
- **Land Search**: `propbolt.com/land` - Admin panel for internal use only
- **API Access**: `propbolt.com/access` - Customer API key management
- **Account Types**:
  - `land` - Internal admin access to land search dashboard
  - `data` - Customer API access for property data endpoints
  - `NULL` - No access (prevents login)

### Key Directories
```
/devtesting/          # Search endpoint testing (KEEP INTACT)
/src/                 # Next.js frontend components
/database/            # Database connection and models
/search/              # Search functionality
/propbolthelper/      # Helper utilities
```

### Deployment Workflow
1. **Development**: Test locally on localhost:3000 and localhost:8080
2. **Backend Deploy**: `gcloud app deploy app.yaml` (targets propbolt.com/api)
3. **Frontend Deploy**: `gcloud app deploy frontend-app.yaml` (targets propbolt.com)
4. **Routing**: Uses `dispatch.yaml` for traffic routing

### Database Configuration
- **Instance**: `propbolt-postgres` (us-central1-f)
- **Connection**: Cloud SQL PostgreSQL
- **Tables**: users, properties, watchlist_items, search_queries
- **Authentication**: Account type system (land/data users)

### Search Endpoints
- **Current Issue**: Zillow proxy getting 403 errors
- **Development Files**: Located in `/devtesting/` folder
- **Testing**: Use devtesting files for new search implementations

### Map Services
- **Current**: Mapbox integration
- **Alternative**: Google Cloud mapping services available
- **API Key**: Configured for PropBolt domain usage

### Production Features
- **Load Balancing**: Google App Engine auto-scaling
- **Monitoring**: Google Cloud Logging and Monitoring
- **Backups**: Automated Cloud SQL backups
- **SSL**: Managed certificates for propbolt.com

## Development Guidelines

### DO NOT:
- Modify or reference PropBolt project files
- Cross-contaminate between projects
- Use PropBolt-specific configurations in this project

### DO:
- Keep projects completely separate
- Use proper Google Cloud project isolation
- Maintain clear development/production boundaries
- Test thoroughly before deployment

## Contact & Support
- **Developer**: Bryce Bayens
- **Repository**: https://github.com/brycebayens/v1-go
- **Environment**: Google Cloud Platform (gold-braid-458901-v2)
