package realestateapi

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strings"
	"time"
)

// Client represents the RealEstateAPI.com client with improved error handling
type Client struct {
	APIKey     string
	BaseURL    string
	client     *http.Client
	retryCount int
	timeout    time.Duration
}

// NewClient creates a new RealEstateAPI client with improved configuration
func NewClient() *Client {
	apiKey := os.Getenv("REAL_ESTATE_API_KEY")
	if apiKey == "" {
		panic("REAL_ESTATE_API_KEY environment variable is required")
	}

	baseURL := os.Getenv("REAL_ESTATE_API_URL")
	if baseURL == "" {
		baseURL = "https://api.realestateapi.com"
	}

	return &Client{
		APIKey:     apiKey,
		BaseURL:    strings.TrimSuffix(baseURL, "/"), // Remove trailing slash
		retryCount: 3,
		timeout:    45 * time.Second, // Increased timeout for complex searches
		client: &http.Client{
			Timeout: 45 * time.Second,
		},
	}
}

// APIError represents a structured API error response
type APIError struct {
	StatusCode int    `json:"statusCode"`
	Message    string `json:"message"`
	Details    string `json:"details,omitempty"`
	Timestamp  string `json:"timestamp,omitempty"`
}

func (e *APIError) Error() string {
	return fmt.Sprintf("API Error %d: %s", e.StatusCode, e.Message)
}

// makeRequest makes an HTTP request with improved error handling and retry logic
func (c *Client) makeRequest(method, endpoint string, payload interface{}) ([]byte, error) {
	var body io.Reader
	var jsonData []byte
	var err error

	if payload != nil {
		jsonData, err = json.Marshal(payload)
		if err != nil {
			return nil, fmt.Errorf("error marshaling request payload: %v", err)
		}
		body = bytes.NewBuffer(jsonData)
	}

	// Ensure endpoint starts with /
	if !strings.HasPrefix(endpoint, "/") {
		endpoint = "/" + endpoint
	}

	url := c.BaseURL + endpoint
	
	// Retry logic
	for attempt := 0; attempt <= c.retryCount; attempt++ {
		if attempt > 0 {
			log.Printf("Retrying request to %s (attempt %d/%d)", url, attempt+1, c.retryCount+1)
			time.Sleep(time.Duration(attempt) * time.Second) // Exponential backoff
			
			// Reset body for retry
			if payload != nil {
				body = bytes.NewBuffer(jsonData)
			}
		}

		req, err := http.NewRequest(method, url, body)
		if err != nil {
			return nil, fmt.Errorf("error creating request: %v", err)
		}

		// Set headers
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-API-Key", c.APIKey)
		req.Header.Set("User-Agent", "PropBolt-Brain/1.0")
		req.Header.Set("Accept", "application/json")

		log.Printf("Making request to: %s", url)
		if payload != nil {
			log.Printf("Request payload: %s", string(jsonData))
		}

		resp, err := c.client.Do(req)
		if err != nil {
			if attempt == c.retryCount {
				return nil, fmt.Errorf("error making request after %d attempts: %v", c.retryCount+1, err)
			}
			log.Printf("Request failed (attempt %d): %v", attempt+1, err)
			continue
		}

		responseBody, err := io.ReadAll(resp.Body)
		resp.Body.Close()
		
		if err != nil {
			if attempt == c.retryCount {
				return nil, fmt.Errorf("error reading response body: %v", err)
			}
			log.Printf("Error reading response body (attempt %d): %v", attempt+1, err)
			continue
		}

		log.Printf("Response status: %d, body length: %d", resp.StatusCode, len(responseBody))

		// Handle different status codes
		switch resp.StatusCode {
		case http.StatusOK:
			return responseBody, nil
		case http.StatusTooManyRequests:
			if attempt < c.retryCount {
				log.Printf("Rate limited, waiting before retry...")
				time.Sleep(5 * time.Second)
				continue
			}
			fallthrough
		case http.StatusInternalServerError, http.StatusBadGateway, http.StatusServiceUnavailable:
			if attempt < c.retryCount {
				log.Printf("Server error %d, retrying...", resp.StatusCode)
				continue
			}
			fallthrough
		default:
			// Try to parse error response
			var apiError APIError
			if err := json.Unmarshal(responseBody, &apiError); err == nil {
				apiError.StatusCode = resp.StatusCode
				return nil, &apiError
			}
			
			return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(responseBody))
		}
	}

	return nil, fmt.Errorf("request failed after %d attempts", c.retryCount+1)
}

// validateResponse validates the response structure before unmarshaling
func (c *Client) validateResponse(responseBody []byte, result interface{}) error {
	// Check if response is valid JSON
	var temp interface{}
	if err := json.Unmarshal(responseBody, &temp); err != nil {
		return fmt.Errorf("invalid JSON response: %v", err)
	}

	// Unmarshal into the actual result
	if err := json.Unmarshal(responseBody, result); err != nil {
		return fmt.Errorf("error unmarshaling response: %v", err)
	}

	return nil
}

// AutoComplete calls the AutoComplete API with improved error handling
func (c *Client) AutoComplete(input string) (*AutoCompleteResponse, error) {
	if strings.TrimSpace(input) == "" {
		return nil, fmt.Errorf("input cannot be empty")
	}

	payload := AutoCompleteRequest{
		Input: strings.TrimSpace(input),
	}

	responseBody, err := c.makeRequest("POST", "/v2/AutoComplete", payload)
	if err != nil {
		return nil, err
	}

	var response AutoCompleteResponse
	if err := c.validateResponse(responseBody, &response); err != nil {
		return nil, err
	}

	return &response, nil
}

// PropertyMapping calls the Property Mapping API with validation
func (c *Client) PropertyMapping(query interface{}) (*PropertyMappingResponse, error) {
	if query == nil {
		return nil, fmt.Errorf("query cannot be nil")
	}

	payload := PropertyMappingRequest{
		Query: query,
	}

	responseBody, err := c.makeRequest("POST", "/v2/PropertyMapping", payload)
	if err != nil {
		return nil, err
	}

	var response PropertyMappingResponse
	if err := c.validateResponse(responseBody, &response); err != nil {
		return nil, err
	}

	return &response, nil
}

// PropertyDetail calls the Property Detail API with validation
func (c *Client) PropertyDetail(address, propertyID string, includeComps bool) (*PropertyDetailResponse, error) {
	if strings.TrimSpace(address) == "" && strings.TrimSpace(propertyID) == "" {
		return nil, fmt.Errorf("either address or propertyID must be provided")
	}

	payload := PropertyDetailRequest{
		Address:    strings.TrimSpace(address),
		PropertyID: strings.TrimSpace(propertyID),
		Comps:      includeComps,
	}

	responseBody, err := c.makeRequest("POST", "/v2/PropertyDetail", payload)
	if err != nil {
		return nil, err
	}

	var response PropertyDetailResponse
	if err := c.validateResponse(responseBody, &response); err != nil {
		return nil, err
	}

	return &response, nil
}

// PropertyDetailBulk calls the Property Detail Bulk API with validation
func (c *Client) PropertyDetailBulk(propertyIDs []string, includeComps bool) (*PropertyDetailBulkResponse, error) {
	if len(propertyIDs) == 0 {
		return nil, fmt.Errorf("propertyIDs cannot be empty")
	}
	
	if len(propertyIDs) > 1000 {
		return nil, fmt.Errorf("maximum 1000 property IDs allowed per request, got %d", len(propertyIDs))
	}

	// Clean property IDs
	cleanIDs := make([]string, 0, len(propertyIDs))
	for _, id := range propertyIDs {
		if cleaned := strings.TrimSpace(id); cleaned != "" {
			cleanIDs = append(cleanIDs, cleaned)
		}
	}

	if len(cleanIDs) == 0 {
		return nil, fmt.Errorf("no valid property IDs provided")
	}

	payload := PropertyDetailBulkRequest{
		PropertyIDs: cleanIDs,
		Comps:       includeComps,
	}

	responseBody, err := c.makeRequest("POST", "/v2/PropertyDetailBulk", payload)
	if err != nil {
		return nil, err
	}

	var response PropertyDetailBulkResponse
	if err := c.validateResponse(responseBody, &response); err != nil {
		return nil, err
	}

	return &response, nil
}

// PropertySearch calls the Property Search API with validation
func (c *Client) PropertySearch(query interface{}, limit, offset int, idsOnly bool) (*PropertySearchResponse, error) {
	if query == nil {
		return nil, fmt.Errorf("query cannot be nil")
	}

	// Validate pagination parameters
	if limit < 0 || limit > 1000 {
		limit = 25 // Default limit
	}
	if offset < 0 {
		offset = 0
	}

	payload := PropertySearchRequest{
		Query:   query,
		Limit:   limit,
		Offset:  offset,
		IDsOnly: idsOnly,
	}

	responseBody, err := c.makeRequest("POST", "/v2/PropertySearch", payload)
	if err != nil {
		return nil, err
	}

	var response PropertySearchResponse
	if err := c.validateResponse(responseBody, &response); err != nil {
		return nil, err
	}

	return &response, nil
}

// InvoluntaryLiens calls the Involuntary Liens API with validation
func (c *Client) InvoluntaryLiens(address, propertyID string) (*InvoluntaryLiensResponse, error) {
	if strings.TrimSpace(address) == "" && strings.TrimSpace(propertyID) == "" {
		return nil, fmt.Errorf("either address or propertyID must be provided")
	}

	payload := InvoluntaryLiensRequest{
		Address:    strings.TrimSpace(address),
		PropertyID: strings.TrimSpace(propertyID),
	}

	responseBody, err := c.makeRequest("POST", "/v2/InvoluntaryLiens", payload)
	if err != nil {
		return nil, err
	}

	var response InvoluntaryLiensResponse
	if err := c.validateResponse(responseBody, &response); err != nil {
		return nil, err
	}

	return &response, nil
}

// PropertyCompsV3 calls the Property Comps v3 API with validation
func (c *Client) PropertyCompsV3(address string, customParams, boostParams map[string]interface{}, maxComps int, radiusMiles float64) (*PropertyCompsV3Response, error) {
	if strings.TrimSpace(address) == "" {
		return nil, fmt.Errorf("address cannot be empty")
	}

	// Set defaults
	if maxComps <= 0 || maxComps > 50 {
		maxComps = 10
	}
	if radiusMiles <= 0 || radiusMiles > 25 {
		radiusMiles = 2.0
	}

	payload := PropertyCompsV3Request{
		Address:           strings.TrimSpace(address),
		CustomParameters:  customParams,
		BoostParameters:   boostParams,
		MaxComps:          maxComps,
		RadiusMiles:       radiusMiles,
	}

	responseBody, err := c.makeRequest("POST", "/v3/PropertyComps", payload)
	if err != nil {
		return nil, err
	}

	var response PropertyCompsV3Response
	if err := c.validateResponse(responseBody, &response); err != nil {
		return nil, err
	}

	return &response, nil
}

// PropertyCompsV2 calls the Property Comps v2 API with validation
func (c *Client) PropertyCompsV2(address string, maxComps int, radiusMiles float64) (*PropertyCompsV2Response, error) {
	if strings.TrimSpace(address) == "" {
		return nil, fmt.Errorf("address cannot be empty")
	}

	// Set defaults
	if maxComps <= 0 || maxComps > 50 {
		maxComps = 10
	}
	if radiusMiles <= 0 || radiusMiles > 25 {
		radiusMiles = 2.0
	}

	payload := PropertyCompsV2Request{
		Address:     strings.TrimSpace(address),
		MaxComps:    maxComps,
		RadiusMiles: radiusMiles,
	}

	responseBody, err := c.makeRequest("POST", "/v2/PropertyComps", payload)
	if err != nil {
		return nil, err
	}

	var response PropertyCompsV2Response
	if err := c.validateResponse(responseBody, &response); err != nil {
		return nil, err
	}

	return &response, nil
}

// SkipTrace calls the SkipTrace API with validation
func (c *Client) SkipTrace(firstName, lastName, address, city, state, zip string) (*SkipTraceResponse, error) {
	// Validate required fields
	if strings.TrimSpace(address) == "" {
		return nil, fmt.Errorf("address is required")
	}
	if strings.TrimSpace(city) == "" {
		return nil, fmt.Errorf("city is required")
	}
	if strings.TrimSpace(state) == "" {
		return nil, fmt.Errorf("state is required")
	}

	payload := SkipTraceRequest{
		FirstName: strings.TrimSpace(firstName),
		LastName:  strings.TrimSpace(lastName),
		Address:   strings.TrimSpace(address),
		City:      strings.TrimSpace(city),
		State:     strings.TrimSpace(state),
		Zip:       strings.TrimSpace(zip),
	}

	// SkipTrace API uses v1 endpoint
	responseBody, err := c.makeRequest("POST", "/v1/SkipTrace", payload)
	if err != nil {
		return nil, err
	}

	var response SkipTraceResponse
	if err := c.validateResponse(responseBody, &response); err != nil {
		return nil, err
	}

	return &response, nil
}

// SetRetryCount sets the number of retry attempts for failed requests
func (c *Client) SetRetryCount(count int) {
	if count >= 0 && count <= 10 {
		c.retryCount = count
	}
}

// SetTimeout sets the request timeout
func (c *Client) SetTimeout(timeout time.Duration) {
	if timeout > 0 {
		c.timeout = timeout
		c.client.Timeout = timeout
	}
}
