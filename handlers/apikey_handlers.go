package handlers

import (
    "database/sql"
    "encoding/json"
    "net/http"
    "strconv"

    "github.com/brycebayens/v1-go/models"
)

// UserAPIKeysHandler handles listing a user's API keys
func UserAPIKeysHandler(db *sql.DB) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        if r.Method != http.MethodGet {
            http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
            return
        }

        // Get user ID from context (set by auth middleware)
        userID, ok := r.Context().Value("userID").(int)
        if !ok {
            http.Error(w, "Unauthorized", http.StatusUnauthorized)
            return
        }

        apiKeys, err := models.GetAPIKeysByUserID(db, userID)
        if err != nil {
            http.Error(w, "Failed to retrieve API keys: "+err.Error(), http.StatusInternalServerError)
            return
        }

        // Mask actual API keys for security (show only first/last few chars)
        for _, key := range apiKeys {
            if len(key.APIKey) > 8 {
                masked := key.APIKey[:4] + "..." + key.APIKey[len(key.APIKey)-4:]
                key.APIKey = masked
            }
        }

        w.Header().Set("Content-Type", "application/json")
        json.NewEncoder(w).Encode(map[string]interface{}{
            "success": true,
            "data":    apiKeys,
        })
    }
}

// CreateAPIKeyHandler handles creating a new API key
func CreateAPIKeyHandler(db *sql.DB) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        if r.Method != http.MethodPost {
            http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
            return
        }

        // Get user ID from context (set by auth middleware)
        userID, ok := r.Context().Value("userID").(int)
        if !ok {
            http.Error(w, "Unauthorized", http.StatusUnauthorized)
            return
        }

        // Parse request body
        var requestBody struct {
            KeyName   string `json:"key_name"`
            RateLimit int    `json:"rate_limit"`
        }

        if err := json.NewDecoder(r.Body).Decode(&requestBody); err != nil {
            http.Error(w, "Invalid request body", http.StatusBadRequest)
            return
        }

        // Validate input
        if requestBody.KeyName == "" {
            http.Error(w, "Key name is required", http.StatusBadRequest)
            return
        }

        // Set default rate limit if not provided
        if requestBody.RateLimit <= 0 {
            requestBody.RateLimit = 1000 // Default rate limit
        }

        // Create API key
        apiKey, err := models.CreateAPIKey(db, userID, requestBody.KeyName, requestBody.RateLimit)
        if err != nil {
            http.Error(w, "Failed to create API key: "+err.Error(), http.StatusInternalServerError)
            return
        }

        w.Header().Set("Content-Type", "application/json")
        w.WriteHeader(http.StatusCreated)
        json.NewEncoder(w).Encode(map[string]interface{}{
            "success": true,
            "data":    apiKey,
            "message": "API key created successfully",
        })
    }
}

// DeleteAPIKeyHandler handles deleting an API key
func DeleteAPIKeyHandler(db *sql.DB) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        if r.Method != http.MethodDelete {
            http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
            return
        }

        // Get user ID from context (set by auth middleware)
        userID, ok := r.Context().Value("userID").(int)
        if !ok {
            http.Error(w, "Unauthorized", http.StatusUnauthorized)
            return
        }

        // Parse request body
        var requestBody struct {
            KeyID int `json:"key_id"`
        }

        if err := json.NewDecoder(r.Body).Decode(&requestBody); err != nil {
            http.Error(w, "Invalid request body", http.StatusBadRequest)
            return
        }

        // Validate input
        if requestBody.KeyID <= 0 {
            http.Error(w, "Valid key ID is required", http.StatusBadRequest)
            return
        }

        // Delete API key
        err := models.DeleteAPIKey(db, requestBody.KeyID, userID)
        if err != nil {
            http.Error(w, "Failed to delete API key: "+err.Error(), http.StatusInternalServerError)
            return
        }

        w.Header().Set("Content-Type", "application/json")
        json.NewEncoder(w).Encode(map[string]interface{}{
            "success": true,
            "message": "API key deleted successfully",
        })
    }
}

// APIKeyUsageHandler handles retrieving API key usage statistics
func APIKeyUsageHandler(db *sql.DB) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        if r.Method != http.MethodGet {
            http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
            return
        }

        // Get user ID from context (set by auth middleware)
        userID, ok := r.Context().Value("userID").(int)
        if !ok {
            http.Error(w, "Unauthorized", http.Status