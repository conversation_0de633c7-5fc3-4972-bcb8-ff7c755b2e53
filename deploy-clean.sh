#!/bin/bash

# PropBolt Clean Infrastructure Deployment Script
# This script sets up clean infrastructure for PropBolt with proper SSL and domain routing

set -e  # Exit on any error

echo "🚀 Starting PropBolt Clean Infrastructure Deployment..."

# Configuration
PROJECT_ID="gold-braid-458901-v2"
REGION="us-central1"
ZONE="us-central1-a"

# Domain configuration
DOMAINS=(
    "propbolt.com"
    "www.propbolt.com"
)

echo "📋 Project: $PROJECT_ID"
echo "🌍 Region: $REGION"
echo "🏷️  Domains: ${DOMAINS[*]}"

# Set the project
echo "🔧 Setting Google Cloud project..."
gcloud config set project $PROJECT_ID

# Clean up existing resources
echo "🧹 Cleaning up existing resources..."

# Delete existing App Engine versions (keep one serving version)
echo "  - Cleaning up App Engine versions..."
gcloud app versions list --format="value(id,service)" | while read version service; do
    if [[ "$version" != "$(gcloud app versions list --service=$service --format='value(id)' --sort-by='~createTime' --limit=1)" ]]; then
        echo "    Deleting version $version from service $service..."
        gcloud app versions delete $version --service=$service --quiet || true
    fi
done

# Delete storage buckets (except staging bucket)
echo "  - Cleaning up storage buckets..."
gsutil ls | grep -v "staging\|artifacts" | while read bucket; do
    echo "    Deleting bucket $bucket..."
    gsutil -m rm -r $bucket || true
done

# Clean up Cloud SQL instances (be careful with this)
echo "  - Listing Cloud SQL instances (manual cleanup required)..."
gcloud sql instances list

echo "⚠️  Please manually delete any unnecessary Cloud SQL instances if needed."
echo "⚠️  Current instances are listed above."

# Build and prepare backend
echo "🔨 Building Go backend..."
go mod tidy
go build -o propbolt-brain .

# Deploy backend (default service)
echo "🚀 Deploying Go backend to App Engine (default service)..."
gcloud app deploy app.yaml --quiet

# Deploy frontend
echo "🚀 Deploying Next.js frontend to App Engine (frontend service)..."
gcloud app deploy frontend-app.yaml --quiet

# Deploy dispatch configuration
echo "🔧 Deploying dispatch configuration..."
gcloud app deploy dispatch.yaml --quiet

# Set up SSL certificates for custom domains
echo "🔒 Setting up SSL certificates for custom domains..."
for domain in "${DOMAINS[@]}"; do
    echo "  - Setting up SSL for $domain..."
    gcloud app domain-mappings create $domain --quiet || echo "    Domain $domain already exists or failed to create"
done

# Verify deployment
echo "✅ Verifying deployment..."
echo "  - App Engine services:"
gcloud app services list

echo "  - App Engine versions:"
gcloud app versions list

echo "  - Domain mappings:"
gcloud app domain-mappings list

# Display important URLs
echo ""
echo "🎉 Deployment completed successfully!"
echo ""
echo "📍 Important URLs:"
echo "  - Frontend: https://propbolt.com"
echo "  - API: https://propbolt.com/api"
echo ""
echo "🔐 Login Flow:"
echo "  - propbolt.com/login → main application"
echo ""
echo "⚠️  Next Steps:"
echo "  1. Update DNS records to point domains to Google App Engine"
echo "  2. Verify SSL certificates are active"
echo "  3. Test all endpoints and routing"
echo "  4. Set up monitoring and logging"
echo ""
echo "🔍 Monitoring:"
echo "  - Logs: gcloud app logs tail -s default"
echo "  - Logs: gcloud app logs tail -s frontend"
echo ""
