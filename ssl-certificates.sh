#!/bin/bash

# PropBolt SSL Certificate Management Script
# Manages SSL certificates for all 6 PropBolt domains

set -e

PROJECT_ID="gold-braid-458901-v2"
DOMAINS=("propbolt.com" "www.propbolt.com" "brain.propbolt.com" "api.propbolt.com" "admin.propbolt.com" "go.propbolt.com")

echo "🔒 PropBolt SSL Certificate Management"
echo "📋 Project: $PROJECT_ID"
echo "🌐 Domains: ${DOMAINS[*]}"
echo ""

# Function to create SSL certificates
create_certificates() {
    echo "🔨 Creating SSL certificates for all PropBolt domains..."
    echo ""
    
    for domain in "${DOMAINS[@]}"; do
        cert_name="${domain//./-}-ssl-cert"
        echo "🌐 Processing: $domain"
        echo "📜 Certificate name: $cert_name"
        
        # Create domain mapping if it doesn't exist
        echo "  🗺️ Ensuring domain mapping exists..."
        sudo gcloud app domain-mappings create $domain \
            --project=$PROJECT_ID \
            --quiet 2>/dev/null || echo "  ✅ Domain mapping already exists"
        
        # Create SSL certificate
        echo "  🔒 Creating SSL certificate..."
        if sudo gcloud app ssl-certificates create "$cert_name" \
            --domains=$domain \
            --project=$PROJECT_ID \
            --quiet 2>/dev/null; then
            echo "  ✅ SSL certificate created successfully"
        else
            echo "  ✅ SSL certificate already exists or in progress"
        fi
        echo ""
    done
    
    echo "🎉 SSL certificate creation completed!"
}

# Function to check certificate status
check_certificates() {
    echo "🔍 Checking SSL certificate status for all domains..."
    echo ""
    
    for domain in "${DOMAINS[@]}"; do
        cert_name="${domain//./-}-ssl-cert"
        echo "🔒 Domain: $domain"
        echo "📜 Certificate: $cert_name"
        
        if cert_info=$(sudo gcloud app ssl-certificates describe "$cert_name" \
            --project=$PROJECT_ID \
            --format="value(managedCertificate.status,domainMappingsCount,managedCertificate.domainStatus)" 2>/dev/null); then
            
            status=$(echo "$cert_info" | cut -d$'\t' -f1)
            mappings=$(echo "$cert_info" | cut -d$'\t' -f2)
            domain_status=$(echo "$cert_info" | cut -d$'\t' -f3)
            
            echo "  📊 Status: $status"
            echo "  🗺️ Domain mappings: $mappings"
            echo "  🌐 Domain status: $domain_status"
            
            case "$status" in
                "ACTIVE")
                    echo "  ✅ Certificate is ACTIVE and working"
                    ;;
                "PROVISIONING")
                    echo "  ⏳ Certificate is being provisioned (up to 15 minutes)"
                    ;;
                "FAILED_RETRYING_NOT_VISIBLE")
                    echo "  🔄 Google is retrying certificate creation"
                    ;;
                "FAILED_RETRYING_CAA_FORBIDDEN")
                    echo "  ⚠️ DNS CAA record may be blocking certificate"
                    ;;
                *)
                    echo "  📊 Current status: $status"
                    ;;
            esac
        else
            echo "  ❌ Certificate not found"
        fi
        echo ""
    done
}

# Function to list all certificates
list_certificates() {
    echo "📋 Listing all SSL certificates..."
    sudo gcloud app ssl-certificates list --project=$PROJECT_ID
}

# Function to delete certificates
delete_certificates() {
    echo "🗑️ Deleting SSL certificates for all PropBolt domains..."
    echo "⚠️ This will remove SSL certificates - are you sure? (y/N)"
    read -r confirmation
    
    if [[ $confirmation =~ ^[Yy]$ ]]; then
        for domain in "${DOMAINS[@]}"; do
            cert_name="${domain//./-}-ssl-cert"
            echo "🗑️ Deleting certificate for $domain..."
            sudo gcloud app ssl-certificates delete "$cert_name" \
                --project=$PROJECT_ID \
                --quiet || echo "Certificate $cert_name not found"
        done
        echo "✅ Certificate deletion completed"
    else
        echo "❌ Certificate deletion cancelled"
    fi
}

# Function to show help
show_help() {
    echo "PropBolt SSL Certificate Management"
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  create    - Create SSL certificates for all 6 domains"
    echo "  check     - Check status of all SSL certificates"
    echo "  list      - List all SSL certificates"
    echo "  delete    - Delete all SSL certificates (with confirmation)"
    echo "  help      - Show this help message"
    echo ""
    echo "Domains managed:"
    for domain in "${DOMAINS[@]}"; do
        echo "  - $domain"
    done
}

# Main script logic
case "${1:-check}" in
    "create")
        create_certificates
        ;;
    "check")
        check_certificates
        ;;
    "list")
        list_certificates
        ;;
    "delete")
        delete_certificates
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    *)
        echo "❌ Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac

echo ""
echo "🔧 Useful commands:"
echo "  Check specific certificate: sudo gcloud app ssl-certificates describe CERT_NAME --project=$PROJECT_ID"
echo "  Monitor App Engine: https://console.cloud.google.com/appengine?project=$PROJECT_ID"
echo "  View domain mappings: sudo gcloud app domain-mappings list --project=$PROJECT_ID"
