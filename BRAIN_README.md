# PropBolt Brain - Admin Vacant Land Analysis Platform

A production-grade, admin-only platform for advanced vacant land search, analysis, and user management. Built with Go backend and integrates with RealEstateAPI.com for comprehensive property data.

## 🏗️ Architecture Overview

PropBolt Brain consists of:
- **Legacy PropBolt API**: Existing property search and analysis functionality
- **RealEstateAPI.com Proxy Layer**: Secure server-side integration with external APIs
- **Admin Authentication**: BetterAuth integration for role-based access control
- **Google Cloud Infrastructure**: Production deployment on GCP

## 🚀 Features

### Core Functionality
- **Property Details**: Comprehensive property information by ID, URL, or address
- **Enhanced Search**: Advanced filtering with vacant land specific parameters
- **Mapping Integration**: Interactive maps with property pins and boundaries
- **Commercial Analysis**: Business compatibility scoring and ROI projections
- **Watch List Management**: Save and categorize properties of interest
- **Market Analytics**: Trend analysis and price predictions

### RealEstateAPI.com Integration
- **AutoComplete**: Address suggestions and location autocomplete
- **Property Mapping**: Map pin locations for visualization
- **Property Details**: Comprehensive property data including owner info
- **Bulk Operations**: Process up to 1000 properties simultaneously
- **Property Search**: Advanced search with complex filtering
- **Liens Information**: Involuntary liens and legal data
- **Comparables**: Property comps with v2 and v3 algorithms
- **SkipTrace**: Owner contact information and demographics

### Admin Features
- **Role-Based Access**: Admin-only authentication via propbolt.com/login
- **User Management**: Admin dashboard for user oversight
- **System Configuration**: API endpoint testing and monitoring
- **Analytics Dashboard**: Usage metrics and system health

## 🛠️ Technology Stack

- **Backend**: Go 1.22+ with Gorilla Mux and CORS
- **Database**: Google Cloud PostgreSQL
- **Authentication**: BetterAuth with role-based access
- **Maps**: Mapbox integration
- **Deployment**: Google Cloud Run with auto-scaling
- **Monitoring**: Google Cloud Logging + LogRocket
- **Storage**: Google Cloud Storage
- **Cache**: Redis for API response caching

## 📋 Prerequisites

- Go 1.22 or higher
- Google Cloud SDK
- Docker (for containerized deployment)
- Admin access to propbolt.com authentication system

## 🚀 Quick Start

### 1. Environment Setup

```bash
# Clone the repository
git clone <repository-url>
cd propbolt-brain

# Edit .env with your production credentials
nano .env
```

### 2. Local Development

```bash
# Install dependencies
go mod download

# Set required environment variables
export PORT=8080
export REAL_ESTATE_API_KEY="your-api-key"

# Run the application
go run main.go
```

### 3. Production Deployment

```bash
# Make deployment script executable
chmod +x deploy.sh

# Deploy to Google Cloud Platform
sudo ./deploy.sh
```

## 📚 API Documentation

### Legacy PropBolt Endpoints

#### Health Check
```bash
GET /
```

#### Property Details
```bash
GET /property?address=123 Main St, Daytona Beach, FL&listingPhotos=true
```

#### Enhanced Land Search
```bash
GET /search/for-sale-enhanced?neLat=29.2200&neLong=-81.0100&swLat=29.2000&swLong=-81.0300&lotSizeMin=5000&hasUtilities=true&zoningType=residential
```

### RealEstateAPI.com Proxy Endpoints

All proxy endpoints require admin authentication and use POST with JSON payloads:

#### AutoComplete
```bash
POST /api/v1/proxy/autocomplete
Content-Type: application/json

{
  "input": "123 Main St, Daytona"
}
```

#### Property Detail
```bash
POST /api/v1/proxy/property-detail
Content-Type: application/json

{
  "address": "123 Main St, Daytona Beach, FL",
  "comps": true
}
```

#### Property Search
```bash
POST /api/v1/proxy/property-search
Content-Type: application/json

{
  "query": {
    "location": {
      "city": "Daytona Beach",
      "state": "FL"
    },
    "propertyType": "land",
    "priceRange": {
      "min": 10000,
      "max": 100000
    }
  },
  "limit": 100
}
```

#### SkipTrace
```bash
POST /api/v1/proxy/skiptrace
Content-Type: application/json

{
  "first_name": "John",
  "last_name": "Doe",
  "address": "123 Main St",
  "city": "Daytona Beach",
  "state": "FL",
  "zip": "32114"
}
```

## 🏗️ Project Structure

```
propbolt-brain/
├── main.go                          # Main application entry point
├── .env                            # Environment configuration
├── Dockerfile                      # Container configuration
├── deploy.sh                       # GCP deployment script
├── go.mod                          # Go module dependencies
├── realestateapi/                  # RealEstateAPI.com client
│   └── client.go                   # API client implementation
├── handlers/                       # HTTP request handlers
│   └── proxy.go                    # Proxy endpoint handlers
├── autocomplete/                   # Legacy autocomplete functionality
├── details/                        # Legacy property details
├── search/                         # Legacy property search
├── zestimate/                      # Legacy rent estimation
├── utils/                          # Utility functions
├── propbolthelper/                 # Helper functions
├── public/                         # Static assets
├── docs/                           # Documentation
│   ├── BRAIN_API_DOCUMENTATION.md  # Complete API documentation
│   └── BRAIN_PROPBOLT_TECHNICAL_PLAN.md # Technical implementation plan
└── README.md                       # This file
```

## 🔧 Configuration

### Environment Variables

Key environment variables (see `.env` for complete list):

```bash
# Core Configuration
NODE_ENV=production
PORT=8080

# Authentication
BETTERAUTH_CLIENT_ID="your_client_id"
BETTERAUTH_CLIENT_SECRET="your_secret"

# RealEstateAPI.com Integration
REAL_ESTATE_API_KEY="AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914"
REAL_ESTATE_API_URL="https://api.realestateapi.com/v2"

# Google Cloud Platform
GCP_PROJECT_ID="gold-braid-458901-v2"
GCP_PROJECT_NUMBER="456078002475"
DB_HOST="********"
DB_NAME="brain_propbolt_prod"

# Mapbox
NEXT_PUBLIC_MAPBOX_TOKEN="pk.eyJ1..."
```

## 🚀 Deployment

### Local Development
```bash
PORT=8080 go run main.go
```

### Production Deployment
```bash
# Deploy to Google Cloud Platform
sudo ./deploy.sh

# Or manual deployment
sudo gcloud run deploy brain-api \
    --image gcr.io/gold-braid-458901-v2/brain-api \
    --platform managed \
    --region us-east1 \
    --allow-unauthenticated
```

## 🔒 Security

- **Admin-Only Access**: All endpoints require admin authentication
- **API Key Security**: RealEstateAPI.com key stored server-side only
- **CORS Configuration**: Restricted to authorized domains
- **Rate Limiting**: Implemented to prevent abuse
- **Input Validation**: All inputs validated and sanitized

## 📊 Monitoring

- **Health Checks**: Built-in health monitoring endpoints
- **Google Cloud Logging**: Comprehensive logging and alerting
- **LogRocket Integration**: User session recording for debugging
- **Error Tracking**: Structured error responses with tracking

## 🧪 Testing

```bash
# Run all tests
go test ./...

# Test specific package
go test ./realestateapi

# Test with coverage
go test -cover ./...
```

## 📖 Documentation

- **[Complete API Documentation](BRAIN_API_DOCUMENTATION.md)**: Detailed endpoint documentation
- **[Technical Plan](BRAIN_PROPBOLT_TECHNICAL_PLAN.md)**: Comprehensive implementation plan
- **[Environment Configuration](.env)**: Production environment setup

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is proprietary software. All rights reserved.

## 🆘 Support

For technical support and questions:
- Create an issue in the repository
- Contact the PropBolt Brain development team
- Review the comprehensive documentation in the `docs/` directory

## 🎯 Next Steps

1. **Frontend Development**: Build Next.js admin dashboard
2. **Database Integration**: Implement PostgreSQL schema
3. **Authentication**: Complete BetterAuth integration
4. **Testing**: Comprehensive test suite
5. **Monitoring**: Advanced analytics and alerting

---

**PropBolt Brain** - Empowering admin users with comprehensive vacant land analysis and management capabilities.
